<x-layout.admin title="Add Advisor">
    <div class="mb-8">
        <div class="flex items-center space-x-4">
            <a href="{{ route('admin.advisor.index') }}" class="text-gray-600 hover:text-gray-800">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Add Advisor</h1>
                <p class="text-gray-600">Add a new academic advisor or mentor</p>
            </div>
        </div>
    </div>

    <form method="POST" action="{{ route('admin.advisor.store') }}" class="space-y-6">
        @csrf
        
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-6">Advisor Details</h2>
            
            <div class="grid gap-6">
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                    <input type="text" id="name" name="name" required
                           value="{{ old('name') }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                           placeholder="e.g., Prof. Dr. <PERSON>">
                    @error('name')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="designation" class="block text-sm font-medium text-gray-700 mb-2">Designation *</label>
                    <input type="text" id="designation" name="designation" required
                           value="{{ old('designation') }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                           placeholder="e.g., Professor of Computer Science">
                    @error('designation')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="rank" class="block text-sm font-medium text-gray-700 mb-2">Academic Rank *</label>
                    <select id="rank" name="rank" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                        <option value="">Select Rank</option>
                        <option value="Professor" {{ old('rank') == 'Professor' ? 'selected' : '' }}>Professor</option>
                        <option value="Associate Professor" {{ old('rank') == 'Associate Professor' ? 'selected' : '' }}>Associate Professor</option>
                        <option value="Assistant Professor" {{ old('rank') == 'Assistant Professor' ? 'selected' : '' }}>Assistant Professor</option>
                        <option value="Senior Lecturer" {{ old('rank') == 'Senior Lecturer' ? 'selected' : '' }}>Senior Lecturer</option>
                        <option value="Lecturer" {{ old('rank') == 'Lecturer' ? 'selected' : '' }}>Lecturer</option>
                        <option value="Research Fellow" {{ old('rank') == 'Research Fellow' ? 'selected' : '' }}>Research Fellow</option>
                        <option value="Other" {{ old('rank') == 'Other' ? 'selected' : '' }}>Other</option>
                    </select>
                    @error('rank')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                    <input type="email" id="email" name="email" required
                           value="{{ old('email') }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                           placeholder="<EMAIL>">
                    @error('email')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="image" class="block text-sm font-medium text-gray-700 mb-2">Profile Image Filename</label>
                    <input type="text" id="image" name="image"
                           value="{{ old('image') }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                           placeholder="e.g., advisor1.jpg">
                    <p class="text-sm text-gray-500 mt-1">Upload the image file to /public/asset/images/ directory first</p>
                    @error('image')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex items-center justify-between">
            <a href="{{ route('admin.advisor.index') }}" 
               class="bg-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-400 transition duration-300">
                Cancel
            </a>
            
            <button type="submit" 
                    class="bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition duration-300">
                <i class="fas fa-save mr-2"></i>
                Save Advisor
            </button>
        </div>
    </form>
</x-layout.admin>
