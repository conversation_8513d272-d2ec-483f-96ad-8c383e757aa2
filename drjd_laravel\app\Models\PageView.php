<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PageView extends Model
{
    protected $fillable = [
        'page',
        'count',
    ];

    public static function incrementPageView($page = 'home')
    {
        $pageView = self::firstOrCreate(['page' => $page], ['count' => 0]);
        $pageView->increment('count');
        return $pageView;
    }

    public static function getCount($page = 'home')
    {
        $pageView = self::where('page', $page)->first();
        return $pageView ? $pageView->count : 0;
    }

    public static function getTotalViews()
    {
        return self::sum('count');
    }
}
