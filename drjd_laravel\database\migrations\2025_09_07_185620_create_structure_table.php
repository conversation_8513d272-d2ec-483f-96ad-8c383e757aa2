<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('structure', function (Blueprint $table) {
            $table->id();
            $table->string('honorific')->default('Dr.');
            $table->string('name');
            $table->string('page1')->default('Home');
            $table->string('page2')->default('About');
            $table->string('page3')->default('Journey');
            $table->string('page4')->default('Journals');
            $table->string('page5')->default('Thesis');
            $table->string('page6')->default('Advisors');
            $table->string('page7')->default('Field of Interest');
            $table->string('page8')->default('Contact');
            $table->text('short_bio');
            $table->text('moto');
            $table->longText('about');
            $table->string('page3_title')->default('Education & Experience');
            $table->string('page4_title')->default('Journals & Papers');
            $table->string('page5_title')->default('Thesis & Chapters');
            $table->string('page6_title')->default('Key Advisors');
            $table->string('page7_title')->default('Field of Interest');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('structure');
    }
};
