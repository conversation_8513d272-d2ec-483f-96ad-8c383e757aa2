<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\PageView;

class TrackVisitor
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Track page view only for GET requests and not for admin routes
        if ($request->isMethod('GET') && !$request->is('admin/*')) {
            $page = $request->path();
            $pageName = $this->getPageName($page);
            PageView::incrementPageView($pageName);
        }

        return $next($request);
    }

    /**
     * Convert URL path to page name
     */
    private function getPageName($path)
    {
        if ($path === '/') {
            return 'home';
        }

        return str_replace('/', '', $path);
    }
}
