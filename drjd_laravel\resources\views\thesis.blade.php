<x-layout.app page="thesis">
    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-purple-600 to-pink-600 text-white py-16">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto text-center">
                <h1 class="text-4xl md:text-5xl font-bold mb-6">{{ $structure->page5_title ?? 'Thesis & Chapters' }}</h1>
                <p class="text-xl">Doctoral research and academic thesis work</p>
            </div>
        </div>
    </section>

    <!-- Thesis Items -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="max-w-6xl mx-auto">
                @if($thesisItems->count() > 0)
                    <div class="space-y-8">
                        @foreach($thesisItems as $thesis)
                            <div class="bg-white rounded-lg shadow-lg hover:shadow-xl transition duration-300 overflow-hidden">
                                <div class="p-8">
                                    <div class="flex items-start space-x-6">
                                        <!-- Icon -->
                                        <div class="flex-shrink-0">
                                            @if($thesis->icon)
                                                <img src="{{ asset('asset/icons/' . $thesis->icon) }}" alt="Icon" class="w-16 h-16 rounded">
                                            @else
                                                <div class="w-16 h-16 bg-purple-100 rounded flex items-center justify-center">
                                                    <i class="fas fa-book text-purple-600 text-2xl"></i>
                                                </div>
                                            @endif
                                        </div>

                                        <!-- Content -->
                                        <div class="flex-1">
                                            <h3 class="text-2xl font-bold text-gray-800 mb-4">{{ $thesis->name }}</h3>

                                            <div class="grid md:grid-cols-2 gap-6 mb-6">
                                                <!-- Submission Details -->
                                                <div>
                                                    <h4 class="text-lg font-semibold text-gray-700 mb-3">Submission Details</h4>
                                                    <div class="space-y-2 text-gray-600">
                                                        <p><strong>Submitted by:</strong> {{ $thesis->sub_by_name }}</p>
                                                        <p><strong>Designation:</strong> {{ $thesis->sub_by_designation }}</p>
                                                        <p><strong>Institution:</strong> {{ $thesis->institution }}</p>
                                                    </div>
                                                </div>

                                                <!-- Supervision -->
                                                <div>
                                                    <h4 class="text-lg font-semibold text-gray-700 mb-3">Supervision</h4>
                                                    <div class="space-y-2 text-gray-600">
                                                        <p><strong>Guide 1:</strong> {{ $thesis->guide_1_name }}</p>
                                                        <p class="text-sm text-gray-500">{{ $thesis->guide_1_designation }}</p>

                                                        @if($thesis->guide_2_name)
                                                            <p><strong>Guide 2:</strong> {{ $thesis->guide_2_name }}</p>
                                                            <p class="text-sm text-gray-500">{{ $thesis->guide_2_designation }}</p>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Action Button -->
                                            <div class="flex items-center justify-between">
                                                <a href="{{ $thesis->link }}" target="_blank"
                                                   class="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition duration-300 flex items-center">
                                                    <i class="fas fa-external-link-alt mr-2"></i>
                                                    View Thesis
                                                </a>
                                                <div class="text-sm text-gray-500">
                                                    <i class="fas fa-calendar mr-1"></i>
                                                    {{ date('M Y') }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-16">
                        <i class="fas fa-book text-6xl text-gray-300 mb-6"></i>
                        <h3 class="text-2xl font-bold text-gray-600 mb-4">No Thesis Items Yet</h3>
                        <p class="text-gray-500">Thesis and chapter information will be displayed here once they are added.</p>
                    </div>
                @endif
            </div>
        </div>
    </section>

    <!-- Research Process -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <h2 class="text-3xl font-bold text-center text-gray-800 mb-12">Research Process</h2>

                <div class="grid md:grid-cols-4 gap-8">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-lightbulb text-purple-600 text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-bold text-gray-800 mb-2">Ideation</h3>
                        <p class="text-gray-600 text-sm">Identifying research problems and formulating hypotheses</p>
                    </div>

                    <div class="text-center">
                        <div class="w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-search text-pink-600 text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-bold text-gray-800 mb-2">Investigation</h3>
                        <p class="text-gray-600 text-sm">Conducting thorough literature review and data collection</p>
                    </div>

                    <div class="text-center">
                        <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-cogs text-indigo-600 text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-bold text-gray-800 mb-2">Analysis</h3>
                        <p class="text-gray-600 text-sm">Applying statistical methods and analytical frameworks</p>
                    </div>

                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-file-alt text-blue-600 text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-bold text-gray-800 mb-2">Documentation</h3>
                        <p class="text-gray-600 text-sm">Writing and presenting research findings</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Research Impact -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <h2 class="text-3xl font-bold text-center text-gray-800 mb-12">Research Impact</h2>

                <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                    <div class="text-center p-6 bg-purple-50 rounded-lg">
                        <div class="text-3xl font-bold text-purple-600 mb-2">{{ $thesisItems->count() }}</div>
                        <div class="text-gray-600">Thesis Works</div>
                    </div>
                    <div class="text-center p-6 bg-pink-50 rounded-lg">
                        <div class="text-3xl font-bold text-pink-600 mb-2">200+</div>
                        <div class="text-gray-600">Pages Written</div>
                    </div>
                    <div class="text-center p-6 bg-indigo-50 rounded-lg">
                        <div class="text-3xl font-bold text-indigo-600 mb-2">50+</div>
                        <div class="text-gray-600">References</div>
                    </div>
                    <div class="text-center p-6 bg-blue-50 rounded-lg">
                        <div class="text-3xl font-bold text-blue-600 mb-2">5+</div>
                        <div class="text-gray-600">Advisors</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">Explore More Research</h2>
            <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
                Discover my published papers, research advisors, and areas of academic interest.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('journals') }}" class="bg-purple-600 text-white px-8 py-3 rounded-lg hover:bg-purple-700 transition duration-300">
                    View Publications
                </a>
                <a href="{{ route('advisors') }}" class="bg-pink-600 text-white px-8 py-3 rounded-lg hover:bg-pink-700 transition duration-300">
                    Meet My Advisors
                </a>
            </div>
        </div>
    </section>
</x-layout.app>
