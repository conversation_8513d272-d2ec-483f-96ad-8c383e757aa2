<x-layout.app page="journey">
    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-green-600 to-teal-600 text-white py-16">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto text-center">
                <h1 class="text-4xl md:text-5xl font-bold mb-6">{{ $structure->page3_title ?? 'Education & Experience' }}</h1>
                <p class="text-xl">My academic and professional journey</p>
            </div>
        </div>
    </section>

    <!-- Journey Timeline -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                @if($journeys->count() > 0)
                    <div class="space-y-8">
                        @foreach($journeys as $index => $journey)
                            <div class="flex items-start space-x-6 {{ $index % 2 == 0 ? '' : 'flex-row-reverse space-x-reverse' }}">
                                <!-- Timeline Line -->
                                <div class="flex flex-col items-center">
                                    <div class="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center text-white font-bold">
                                        {{ $index + 1 }}
                                    </div>
                                    @if(!$loop->last)
                                        <div class="w-1 h-16 bg-green-200 mt-4"></div>
                                    @endif
                                </div>
                                
                                <!-- Content -->
                                <div class="flex-1 bg-white rounded-lg shadow-lg p-6 {{ $index % 2 == 0 ? '' : 'text-right' }}">
                                    <div class="flex items-center {{ $index % 2 == 0 ? '' : 'justify-end' }} mb-4">
                                        @if($journey->icon)
                                            <img src="{{ asset('asset/icons/' . $journey->icon) }}" alt="Icon" class="w-8 h-8 {{ $index % 2 == 0 ? 'mr-3' : 'ml-3 order-2' }}">
                                        @endif
                                        <div class="{{ $index % 2 == 0 ? '' : 'order-1' }}">
                                            <h3 class="text-xl font-bold text-gray-800">{{ $journey->title }}</h3>
                                            <p class="text-green-600 font-medium">{{ $journey->date }}</p>
                                        </div>
                                    </div>
                                    
                                    @if($journey->sub_title)
                                        <p class="text-gray-600 leading-relaxed">{{ $journey->sub_title }}</p>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-16">
                        <i class="fas fa-graduation-cap text-6xl text-gray-300 mb-6"></i>
                        <h3 class="text-2xl font-bold text-gray-600 mb-4">No Journey Items Yet</h3>
                        <p class="text-gray-500">Journey items will be displayed here once they are added.</p>
                    </div>
                @endif
            </div>
        </div>
    </section>

    <!-- Skills & Expertise -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <h2 class="text-3xl font-bold text-center text-gray-800 mb-12">Skills & Expertise</h2>
                
                <div class="grid md:grid-cols-2 gap-8">
                    <div>
                        <h3 class="text-xl font-bold text-gray-800 mb-4">Research Areas</h3>
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-green-600 rounded-full mr-3"></div>
                                <span class="text-gray-600">Data Science & Analytics</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-green-600 rounded-full mr-3"></div>
                                <span class="text-gray-600">Machine Learning</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-green-600 rounded-full mr-3"></div>
                                <span class="text-gray-600">Academic Research</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-green-600 rounded-full mr-3"></div>
                                <span class="text-gray-600">Statistical Analysis</span>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h3 class="text-xl font-bold text-gray-800 mb-4">Technical Skills</h3>
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-teal-600 rounded-full mr-3"></div>
                                <span class="text-gray-600">Python & R Programming</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-teal-600 rounded-full mr-3"></div>
                                <span class="text-gray-600">Statistical Software</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-teal-600 rounded-full mr-3"></div>
                                <span class="text-gray-600">Research Methodology</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-teal-600 rounded-full mr-3"></div>
                                <span class="text-gray-600">Academic Writing</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">Explore My Work</h2>
            <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
                Discover my research publications, thesis work, and academic contributions.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('journals') }}" class="bg-green-600 text-white px-8 py-3 rounded-lg hover:bg-green-700 transition duration-300">
                    View Publications
                </a>
                <a href="{{ route('thesis') }}" class="bg-teal-600 text-white px-8 py-3 rounded-lg hover:bg-teal-700 transition duration-300">
                    View Thesis Work
                </a>
            </div>
        </div>
    </section>
</x-layout.app>
