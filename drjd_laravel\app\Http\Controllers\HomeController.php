<?php

namespace App\Http\Controllers;

use App\Models\Structure;
use App\Models\Journey;
use App\Models\JournalPaper;
use App\Models\Advisor;
use App\Models\ThesisChapter;
use App\Models\FieldInterest;
use App\Models\FieldInterestPdf;
use App\Models\FieldInterestMedia;

class HomeController extends Controller
{
    public function index()
    {
        $structure = Structure::first();
        $journeys = Journey::orderBy('id', 'desc')->take(3)->get();
        $journals = JournalPaper::orderBy('id', 'desc')->take(3)->get();
        $advisors = Advisor::orderBy('id', 'desc')->take(3)->get();

        return view('home', compact('structure', 'journeys', 'journals', 'advisors'));
    }

    public function about()
    {
        $structure = Structure::first();
        return view('about', compact('structure'));
    }

    public function journey()
    {
        $structure = Structure::first();
        $journeys = Journey::orderBy('id', 'desc')->get();
        return view('journey', compact('structure', 'journeys'));
    }

    public function journals()
    {
        $structure = Structure::first();
        $journals = JournalPaper::orderBy('id', 'desc')->get();
        return view('journals', compact('structure', 'journals'));
    }

    public function advisors()
    {
        $structure = Structure::first();
        $advisors = Advisor::orderBy('id', 'desc')->get();
        return view('advisors', compact('structure', 'advisors'));
    }

    public function thesis()
    {
        $structure = Structure::first();
        $thesisItems = ThesisChapter::orderBy('id', 'desc')->get();
        return view('thesis', compact('structure', 'thesisItems'));
    }

    public function fieldInterest()
    {
        $structure = Structure::first();
        $fieldInterests = FieldInterest::orderBy('id', 'desc')->get();
        $pdfs = FieldInterestPdf::orderBy('display_date', 'desc')->get();
        $media = FieldInterestMedia::orderBy('display_date', 'desc')->get();
        $homePdfs = FieldInterestPdf::getHomeItems(4);
        $homeMedia = FieldInterestMedia::getHomeItems(4);
        $homeTheses = ThesisChapter::getHomeItems(4);

        return view('field-interest', compact('structure', 'fieldInterests', 'pdfs', 'media', 'homePdfs', 'homeMedia', 'homeTheses'));
    }

    public function fieldInterestPdfsMore()
    {
        $structure = Structure::first();
        $pdfs = FieldInterestPdf::getOtherItems();
        return view('field-interest-pdfs-more', compact('structure', 'pdfs'));
    }

    public function fieldInterestMediaMore()
    {
        $structure = Structure::first();
        $media = FieldInterestMedia::getOtherItems();
        return view('field-interest-media-more', compact('structure', 'media'));
    }

    public function thesisMore()
    {
        $structure = Structure::first();
        $theses = ThesisChapter::getOtherItems();
        return view('thesis-more', compact('structure', 'theses'));
    }

    public function contact()
    {
        $structure = Structure::first();
        return view('contact', compact('structure'));
    }
}
