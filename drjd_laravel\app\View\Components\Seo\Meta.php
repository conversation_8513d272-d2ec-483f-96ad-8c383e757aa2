<?php

namespace App\View\Components\Seo;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;
use App\Models\SeoSetting;

class Meta extends Component
{
    public $seoSettings;

    /**
     * Create a new component instance.
     */
    public function __construct($page = 'home')
    {
        $this->seoSettings = SeoSetting::where('page_name', $page)->first();
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.seo.meta');
    }
}
