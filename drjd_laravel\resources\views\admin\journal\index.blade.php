<x-layout.admin title="Journal Management">
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Journal Management</h1>
                <p class="text-gray-600">Manage your published journals and papers</p>
            </div>
            <a href="{{ route('admin.journal.create') }}" 
               class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition duration-300">
                <i class="fas fa-plus mr-2"></i>
                Add Journal Paper
            </a>
        </div>
    </div>

    @if($journals->count() > 0)
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Icon
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Paper Name
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Link
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($journals as $journal)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($journal->icon)
                                    <div class="flex items-center">
                                        <img src="{{ asset('asset/images/' . $journal->icon) }}" 
                                             alt="Icon" class="h-8 w-8 rounded-full object-cover"
                                             onerror="this.src='{{ asset('asset/images/default-icon.png') }}'">
                                    </div>
                                @else
                                    <div class="h-8 w-8 bg-gray-200 rounded-full flex items-center justify-center">
                                        <i class="fas fa-file-alt text-gray-400"></i>
                                    </div>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900 max-w-md">{{ $journal->name }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <a href="{{ $journal->link }}" target="_blank" 
                                   class="text-indigo-600 hover:text-indigo-900 text-sm">
                                    <i class="fas fa-external-link-alt mr-1"></i>
                                    View Paper
                                </a>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex items-center justify-end space-x-2">
                                    <a href="{{ route('admin.journal.edit', $journal) }}" 
                                       class="text-indigo-600 hover:text-indigo-900">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form method="POST" action="{{ route('admin.journal.destroy', $journal) }}" 
                                          class="inline" onsubmit="return confirm('Are you sure you want to delete this journal paper?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="text-red-600 hover:text-red-900">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    @else
        <div class="bg-white rounded-lg shadow p-6 text-center">
            <div class="text-gray-500 mb-4">
                <i class="fas fa-book text-4xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Journal Papers Found</h3>
            <p class="text-gray-600 mb-4">Start adding your published journals and research papers.</p>
            <a href="{{ route('admin.journal.create') }}" 
               class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition duration-300">
                <i class="fas fa-plus mr-2"></i>
                Add First Journal Paper
            </a>
        </div>
    @endif
</x-layout.admin>
