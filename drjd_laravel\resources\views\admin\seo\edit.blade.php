<x-layout.admin title="Edit SEO Settings">
    <div class="mb-8">
        <div class="flex items-center space-x-4">
            <a href="{{ route('admin.seo.index') }}" class="text-gray-600 hover:text-gray-800">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-900">SEO Settings for {{ ucfirst(str_replace('-', ' ', $page)) }}</h1>
                <p class="text-gray-600">Configure meta tags, Open Graph, and structured data</p>
            </div>
        </div>
    </div>

    <form method="POST" action="{{ route('admin.seo.update', $page) }}" class="space-y-8">
        @csrf
        @method('PUT')
        
        <!-- Basic Meta Tags -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-6">Basic Meta Tags</h2>
            
            <div class="grid gap-6">
                <div>
                    <label for="meta_title" class="block text-sm font-medium text-gray-700 mb-2">Meta Title *</label>
                    <input type="text" id="meta_title" name="meta_title" required
                           value="{{ old('meta_title', $seoSetting->meta_title) }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                           placeholder="Enter meta title (50-60 characters recommended)">
                    <p class="text-sm text-gray-500 mt-1">Current length: <span id="title-length">{{ strlen($seoSetting->meta_title ?? '') }}</span> characters</p>
                    @error('meta_title')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="meta_description" class="block text-sm font-medium text-gray-700 mb-2">Meta Description *</label>
                    <textarea id="meta_description" name="meta_description" rows="3" required
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                              placeholder="Enter meta description (150-160 characters recommended)">{{ old('meta_description', $seoSetting->meta_description) }}</textarea>
                    <p class="text-sm text-gray-500 mt-1">Current length: <span id="description-length">{{ strlen($seoSetting->meta_description ?? '') }}</span> characters</p>
                    @error('meta_description')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="meta_keywords" class="block text-sm font-medium text-gray-700 mb-2">Meta Keywords</label>
                    <input type="text" id="meta_keywords" name="meta_keywords"
                           value="{{ old('meta_keywords', $seoSetting->meta_keywords) }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                           placeholder="keyword1, keyword2, keyword3">
                    <p class="text-sm text-gray-500 mt-1">Separate keywords with commas</p>
                    @error('meta_keywords')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Open Graph Tags -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-6">Open Graph & Social Media</h2>
            
            <div class="grid gap-6">
                <div>
                    <label for="og_title" class="block text-sm font-medium text-gray-700 mb-2">OG Title</label>
                    <input type="text" id="og_title" name="og_title"
                           value="{{ old('og_title', $seoSetting->og_title) }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                           placeholder="Leave empty to use meta title">
                    @error('og_title')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="og_description" class="block text-sm font-medium text-gray-700 mb-2">OG Description</label>
                    <textarea id="og_description" name="og_description" rows="3"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                              placeholder="Leave empty to use meta description">{{ old('og_description', $seoSetting->og_description) }}</textarea>
                    @error('og_description')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="og_image" class="block text-sm font-medium text-gray-700 mb-2">OG Image URL</label>
                    <input type="url" id="og_image" name="og_image"
                           value="{{ old('og_image', $seoSetting->og_image) }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                           placeholder="https://example.com/image.jpg">
                    <p class="text-sm text-gray-500 mt-1">Recommended size: 1200x630 pixels</p>
                    @error('og_image')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Technical SEO -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-6">Technical SEO</h2>
            
            <div class="grid gap-6">
                <div>
                    <label for="canonical_url" class="block text-sm font-medium text-gray-700 mb-2">Canonical URL</label>
                    <input type="url" id="canonical_url" name="canonical_url"
                           value="{{ old('canonical_url', $seoSetting->canonical_url) }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                           placeholder="Leave empty to use current page URL">
                    @error('canonical_url')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="flex items-center">
                        <input type="hidden" name="index_page" value="0">
                        <input type="checkbox" id="index_page" name="index_page" value="1"
                               {{ old('index_page', $seoSetting->index_page) ? 'checked' : '' }}
                               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                        <label for="index_page" class="ml-2 block text-sm text-gray-700">
                            Allow search engines to index this page
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="hidden" name="follow_links" value="0">
                        <input type="checkbox" id="follow_links" name="follow_links" value="1"
                               {{ old('follow_links', $seoSetting->follow_links) ? 'checked' : '' }}
                               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                        <label for="follow_links" class="ml-2 block text-sm text-gray-700">
                            Allow search engines to follow links
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Structured Data -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-6">Structured Data (JSON-LD)</h2>
            
            <div>
                <label for="structured_data" class="block text-sm font-medium text-gray-700 mb-2">JSON-LD Schema</label>
                <textarea id="structured_data" name="structured_data" rows="10"
                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent font-mono text-sm"
                          placeholder='{"@context": "https://schema.org", "@type": "WebPage", "name": "Page Name"}'>{{ old('structured_data', $seoSetting->structured_data ? json_encode($seoSetting->structured_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) : '') }}</textarea>
                <p class="text-sm text-gray-500 mt-1">Enter valid JSON-LD structured data. Leave empty if not needed.</p>
                @error('structured_data')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex items-center justify-between">
            <a href="{{ route('admin.seo.index') }}" 
               class="bg-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-400 transition duration-300">
                Cancel
            </a>
            
            <div class="flex space-x-4">
                <a href="{{ route($page === 'field-interest' ? 'field-interest' : $page) }}" target="_blank"
                   class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition duration-300">
                    <i class="fas fa-external-link-alt mr-2"></i>
                    Preview Page
                </a>
                
                <button type="submit" 
                        class="bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition duration-300">
                    <i class="fas fa-save mr-2"></i>
                    Save Settings
                </button>
            </div>
        </div>
    </form>

    @push('scripts')
    <script>
        // Character counters
        document.getElementById('meta_title').addEventListener('input', function() {
            document.getElementById('title-length').textContent = this.value.length;
        });
        
        document.getElementById('meta_description').addEventListener('input', function() {
            document.getElementById('description-length').textContent = this.value.length;
        });
        
        // JSON validation
        document.getElementById('structured_data').addEventListener('blur', function() {
            if (this.value.trim()) {
                try {
                    JSON.parse(this.value);
                    this.classList.remove('border-red-300');
                    this.classList.add('border-green-300');
                } catch (e) {
                    this.classList.remove('border-green-300');
                    this.classList.add('border-red-300');
                }
            } else {
                this.classList.remove('border-red-300', 'border-green-300');
            }
        });
    </script>
    @endpush
</x-layout.admin>
