<x-layout.admin title="SEO Management">
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">SEO Management</h1>
                <p class="text-gray-600">Manage SEO settings for all pages</p>
            </div>
            <form method="POST" action="{{ route('admin.seo.generate-sitemap') }}" class="inline">
                @csrf
                <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                    <i class="fas fa-sitemap mr-2"></i> Generate Sitemap
                </button>
            </form>
        </div>
    </div>
    
    <!-- SEO Pages Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        @php
            $pages = [
                ['name' => 'home', 'title' => 'Home Page', 'icon' => 'fas fa-home'],
                ['name' => 'about', 'title' => 'About Page', 'icon' => 'fas fa-user'],
                ['name' => 'journey', 'title' => 'Journey Page', 'icon' => 'fas fa-graduation-cap'],
                ['name' => 'journals', 'title' => 'Journals Page', 'icon' => 'fas fa-newspaper'],
                ['name' => 'thesis', 'title' => 'Thesis Page', 'icon' => 'fas fa-book'],
                ['name' => 'advisors', 'title' => 'Advisors Page', 'icon' => 'fas fa-users'],
                ['name' => 'field-interest', 'title' => 'Field of Interest', 'icon' => 'fas fa-lightbulb'],
                ['name' => 'contact', 'title' => 'Contact Page', 'icon' => 'fas fa-envelope'],
            ];
        @endphp
        
        @foreach($pages as $page)
            @php
                $seoSetting = $seoSettings->where('page_name', $page['name'])->first();
            @endphp
            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex items-center mb-4">
                    <i class="{{ $page['icon'] }} text-indigo-600 text-xl mr-3"></i>
                    <h3 class="text-lg font-medium text-gray-900">{{ $page['title'] }}</h3>
                </div>
                
                <div class="space-y-2 mb-4">
                    <div class="flex items-center">
                        <span class="text-sm text-gray-500 w-20">Status:</span>
                        @if($seoSetting)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fas fa-check mr-1"></i> Configured
                            </span>
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                <i class="fas fa-exclamation mr-1"></i> Not Set
                            </span>
                        @endif
                    </div>
                    
                    @if($seoSetting)
                        <div class="text-sm text-gray-600">
                            <strong>Title:</strong> {{ Str::limit($seoSetting->meta_title, 50) }}
                        </div>
                        <div class="text-sm text-gray-600">
                            <strong>Description:</strong> {{ Str::limit($seoSetting->meta_description, 80) }}
                        </div>
                    @endif
                </div>
                
                <div class="flex space-x-2">
                    <a href="{{ route('admin.seo.edit', $page['name']) }}" 
                       class="flex-1 bg-indigo-600 text-white text-center px-3 py-2 rounded text-sm hover:bg-indigo-700">
                        <i class="fas fa-edit mr-1"></i> 
                        {{ $seoSetting ? 'Edit' : 'Configure' }}
                    </a>
                    
                    @if($seoSetting)
                        <a href="{{ route($page['name'] === 'field-interest' ? 'field-interest' : $page['name']) }}" 
                           target="_blank"
                           class="bg-gray-600 text-white px-3 py-2 rounded text-sm hover:bg-gray-700">
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    @endif
                </div>
            </div>
        @endforeach
    </div>
    
    <!-- SEO Tips -->
    <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 class="text-lg font-medium text-blue-900 mb-4">
            <i class="fas fa-lightbulb mr-2"></i> SEO Best Practices
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
            <div>
                <h4 class="font-medium mb-2">Meta Titles:</h4>
                <ul class="space-y-1 list-disc list-inside">
                    <li>Keep between 50-60 characters</li>
                    <li>Include primary keywords</li>
                    <li>Make them unique for each page</li>
                </ul>
            </div>
            <div>
                <h4 class="font-medium mb-2">Meta Descriptions:</h4>
                <ul class="space-y-1 list-disc list-inside">
                    <li>Keep between 150-160 characters</li>
                    <li>Write compelling copy</li>
                    <li>Include a call-to-action</li>
                </ul>
            </div>
            <div>
                <h4 class="font-medium mb-2">Keywords:</h4>
                <ul class="space-y-1 list-disc list-inside">
                    <li>Use relevant, specific keywords</li>
                    <li>Separate with commas</li>
                    <li>Don't keyword stuff</li>
                </ul>
            </div>
            <div>
                <h4 class="font-medium mb-2">Structured Data:</h4>
                <ul class="space-y-1 list-disc list-inside">
                    <li>Use valid JSON-LD format</li>
                    <li>Include relevant schema types</li>
                    <li>Test with Google's tool</li>
                </ul>
            </div>
        </div>
    </div>
</x-layout.admin>
