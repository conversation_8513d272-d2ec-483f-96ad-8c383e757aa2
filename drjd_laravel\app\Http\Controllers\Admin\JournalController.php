<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\JournalPaper;

class JournalController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $journals = JournalPaper::orderBy('id', 'desc')->get();
        return view('admin.journal.index', compact('journals'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.journal.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:500',
            'link' => 'required|url|max:500',
            'icon' => 'nullable|string|max:255',
        ]);

        JournalPaper::create($request->all());

        return redirect()->route('admin.journal.index')
            ->with('success', 'Journal paper created successfully');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(JournalPaper $journal)
    {
        return view('admin.journal.edit', compact('journal'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, JournalPaper $journal)
    {
        $request->validate([
            'name' => 'required|string|max:500',
            'link' => 'required|url|max:500',
            'icon' => 'nullable|string|max:255',
        ]);

        $journal->update($request->all());

        return redirect()->route('admin.journal.index')
            ->with('success', 'Journal paper updated successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(JournalPaper $journal)
    {
        $journal->delete();

        return redirect()->route('admin.journal.index')
            ->with('success', 'Journal paper deleted successfully');
    }
}
