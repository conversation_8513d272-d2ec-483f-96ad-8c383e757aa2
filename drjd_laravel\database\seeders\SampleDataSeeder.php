<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Structure;
use App\Models\Journey;
use App\Models\JournalPaper;
use App\Models\Advisor;

class SampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create structure data
        Structure::firstOrCreate(
            ['id' => 1],
            [
                'honorific' => 'Dr.',
                'name' => 'Jayanta Debbarma',
                'page1' => 'Home',
                'page2' => 'About',
                'page3' => 'Journey',
                'page4' => 'Journals',
                'page5' => 'Thesis',
                'page6' => 'Advisors',
                'page7' => 'Field of Interest',
                'page8' => 'Contact',
                'short_bio' => 'Academic Professional & Researcher',
                'moto' => 'Dedicated to Excellence in Research and Education',
                'about' => 'Welcome to my academic portfolio. I am a dedicated researcher and academic professional with extensive experience in data science, statistical analysis, and research methodology. My work focuses on advancing knowledge through rigorous research and contributing to the academic community through publications, teaching, and collaboration.

Throughout my career, I have been committed to excellence in research and education. I believe in the power of data-driven insights to solve complex problems and make meaningful contributions to society. My research interests span multiple domains, and I am always eager to explore new methodologies and approaches.

I am passionate about mentoring students and young researchers, helping them develop their skills and achieve their academic goals. Collaboration is at the heart of my work, and I welcome opportunities to work with fellow researchers and institutions.',
                'page3_title' => 'Education & Experience',
                'page4_title' => 'Journals & Papers',
                'page5_title' => 'Thesis & Chapters',
                'page6_title' => 'Key Advisors',
                'page7_title' => 'Field of Interest',
            ]
        );

        // Create sample journey items
        $journeys = [
            [
                'icon' => 'graduation.png',
                'title' => 'Ph.D. in Computer Science',
                'sub_title' => 'Specialized in Data Science and Machine Learning',
                'date' => '2018 - 2022',
            ],
            [
                'icon' => 'research.png',
                'title' => 'Research Associate',
                'sub_title' => 'Advanced Research in Statistical Methods',
                'date' => '2016 - 2018',
            ],
            [
                'icon' => 'masters.png',
                'title' => 'M.Sc. in Statistics',
                'sub_title' => 'First Class with Distinction',
                'date' => '2014 - 2016',
            ],
        ];

        foreach ($journeys as $journey) {
            Journey::firstOrCreate(
                ['title' => $journey['title']],
                $journey
            );
        }

        // Create sample journal papers
        $journals = [
            [
                'icon' => 'journal1.png',
                'name' => 'Advanced Statistical Methods in Data Science: A Comprehensive Review',
                'link' => 'https://example.com/paper1',
            ],
            [
                'icon' => 'journal2.png',
                'name' => 'Machine Learning Applications in Academic Research',
                'link' => 'https://example.com/paper2',
            ],
            [
                'icon' => 'journal3.png',
                'name' => 'Innovative Approaches to Data Analysis in Social Sciences',
                'link' => 'https://example.com/paper3',
            ],
        ];

        foreach ($journals as $journal) {
            JournalPaper::firstOrCreate(
                ['name' => $journal['name']],
                $journal
            );
        }

        // Create sample advisors
        $advisors = [
            [
                'image' => 'advisor1.jpg',
                'name' => 'Prof. Dr. John Smith',
                'designation' => 'Professor of Computer Science',
                'rank' => 'Senior Professor',
                'email' => '<EMAIL>',
            ],
            [
                'image' => 'advisor2.jpg',
                'name' => 'Dr. Sarah Johnson',
                'designation' => 'Associate Professor of Statistics',
                'rank' => 'Associate Professor',
                'email' => '<EMAIL>',
            ],
        ];

        foreach ($advisors as $advisor) {
            Advisor::firstOrCreate(
                ['email' => $advisor['email']],
                $advisor
            );
        }

        // Skip thesis and field interest for now as tables may not exist
    }
}
