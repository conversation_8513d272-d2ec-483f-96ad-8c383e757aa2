<x-layout.admin title="Edit Journey Item">
    <div class="mb-8">
        <div class="flex items-center space-x-4">
            <a href="{{ route('admin.journey.index') }}" class="text-gray-600 hover:text-gray-800">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Edit Journey Item</h1>
                <p class="text-gray-600">Update this education or experience item</p>
            </div>
        </div>
    </div>

    <form method="POST" action="{{ route('admin.journey.update', $journey) }}" class="space-y-6">
        @csrf
        @method('PUT')
        
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-6">Journey Item Details</h2>
            
            <div class="grid gap-6">
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Title *</label>
                    <input type="text" id="title" name="title" required
                           value="{{ old('title', $journey->title) }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                           placeholder="e.g., Ph.D. in Computer Science">
                    @error('title')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="sub_title" class="block text-sm font-medium text-gray-700 mb-2">Subtitle</label>
                    <input type="text" id="sub_title" name="sub_title"
                           value="{{ old('sub_title', $journey->sub_title) }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                           placeholder="e.g., Specialized in Data Science and Machine Learning">
                    @error('sub_title')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="date" class="block text-sm font-medium text-gray-700 mb-2">Date *</label>
                    <input type="text" id="date" name="date" required
                           value="{{ old('date', $journey->date) }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                           placeholder="e.g., 2018 - 2022">
                    @error('date')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="icon" class="block text-sm font-medium text-gray-700 mb-2">Icon Filename</label>
                    <input type="text" id="icon" name="icon"
                           value="{{ old('icon', $journey->icon) }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                           placeholder="e.g., graduation.png">
                    <p class="text-sm text-gray-500 mt-1">Upload the icon file to /public/asset/images/ directory first</p>
                    @error('icon')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                @if($journey->icon)
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Current Icon</label>
                        <div class="flex items-center space-x-4">
                            <img src="{{ asset('asset/images/' . $journey->icon) }}" 
                                 alt="Current Icon" class="h-16 w-16 rounded-lg object-cover border"
                                 onerror="this.src='{{ asset('asset/images/default-icon.png') }}'">
                            <div class="text-sm text-gray-600">
                                <p>Filename: {{ $journey->icon }}</p>
                                <p>To change, upload a new file and update the filename above</p>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex items-center justify-between">
            <a href="{{ route('admin.journey.index') }}" 
               class="bg-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-400 transition duration-300">
                Cancel
            </a>
            
            <button type="submit" 
                    class="bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition duration-300">
                <i class="fas fa-save mr-2"></i>
                Update Journey Item
            </button>
        </div>
    </form>
</x-layout.admin>
