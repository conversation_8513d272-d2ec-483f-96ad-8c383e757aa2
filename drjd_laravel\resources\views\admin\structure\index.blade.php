<x-layout.admin title="Site Structure">
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Site Structure</h1>
                <p class="text-gray-600">Manage your site's basic information and navigation</p>
            </div>
            <a href="{{ route('admin.structure.edit') }}" 
               class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition duration-300">
                <i class="fas fa-edit mr-2"></i>
                Edit Structure
            </a>
        </div>
    </div>

    @if($structure)
        <div class="grid gap-6">
            <!-- Basic Information -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Basic Information</h2>
                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                        <p class="text-gray-900">{{ $structure->honorific }} {{ $structure->name }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Short Bio</label>
                        <p class="text-gray-900">{{ $structure->short_bio }}</p>
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Motto</label>
                        <p class="text-gray-900">{{ $structure->moto }}</p>
                    </div>
                </div>
            </div>

            <!-- Navigation Pages -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Navigation Pages</h2>
                <div class="grid md:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Page 1</label>
                        <p class="text-gray-900">{{ $structure->page1 }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Page 2</label>
                        <p class="text-gray-900">{{ $structure->page2 }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Page 3</label>
                        <p class="text-gray-900">{{ $structure->page3 }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Page 4</label>
                        <p class="text-gray-900">{{ $structure->page4 }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Page 5</label>
                        <p class="text-gray-900">{{ $structure->page5 }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Page 6</label>
                        <p class="text-gray-900">{{ $structure->page6 }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Page 7</label>
                        <p class="text-gray-900">{{ $structure->page7 }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Page 8</label>
                        <p class="text-gray-900">{{ $structure->page8 }}</p>
                    </div>
                </div>
            </div>

            <!-- Page Titles -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Page Titles</h2>
                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{{ $structure->page3 }} Title</label>
                        <p class="text-gray-900">{{ $structure->page3_title }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{{ $structure->page4 }} Title</label>
                        <p class="text-gray-900">{{ $structure->page4_title }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{{ $structure->page5 }} Title</label>
                        <p class="text-gray-900">{{ $structure->page5_title }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{{ $structure->page6 }} Title</label>
                        <p class="text-gray-900">{{ $structure->page6_title }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">{{ $structure->page7 }} Title</label>
                        <p class="text-gray-900">{{ $structure->page7_title }}</p>
                    </div>
                </div>
            </div>

            <!-- About Section -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">About Section</h2>
                <div class="prose max-w-none">
                    {!! nl2br(e($structure->about)) !!}
                </div>
            </div>
        </div>
    @else
        <div class="bg-white rounded-lg shadow p-6 text-center">
            <div class="text-gray-500 mb-4">
                <i class="fas fa-sitemap text-4xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Site Structure Found</h3>
            <p class="text-gray-600 mb-4">Set up your site's basic structure and information.</p>
            <a href="{{ route('admin.structure.edit') }}" 
               class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition duration-300">
                <i class="fas fa-plus mr-2"></i>
                Create Structure
            </a>
        </div>
    @endif
</x-layout.admin>
