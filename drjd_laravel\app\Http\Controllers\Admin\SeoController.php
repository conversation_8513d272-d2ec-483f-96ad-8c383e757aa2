<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\SeoSetting;

class SeoController extends Controller
{
    public function index()
    {
        $seoSettings = SeoSetting::all();
        return view('admin.seo.index', compact('seoSettings'));
    }

    public function edit($page)
    {
        $seoSetting = SeoSetting::where('page_name', $page)->first();

        if (!$seoSetting) {
            $seoSetting = new SeoSetting(['page_name' => $page]);
        }

        return view('admin.seo.edit', compact('seoSetting', 'page'));
    }

    public function update(Request $request, $page)
    {
        $request->validate([
            'meta_title' => 'required|string|max:255',
            'meta_description' => 'required|string|max:500',
            'meta_keywords' => 'nullable|string',
            'og_title' => 'nullable|string|max:255',
            'og_description' => 'nullable|string|max:500',
            'og_image' => 'nullable|string',
            'canonical_url' => 'nullable|url',
            'structured_data' => 'nullable|json',
            'index_page' => 'boolean',
            'follow_links' => 'boolean',
        ]);

        $seoSetting = SeoSetting::updateOrCreate(
            ['page_name' => $page],
            $request->only([
                'meta_title',
                'meta_description',
                'meta_keywords',
                'og_title',
                'og_description',
                'og_image',
                'canonical_url',
                'structured_data',
                'index_page',
                'follow_links'
            ])
        );

        return redirect()->route('admin.seo.index')
            ->with('success', 'SEO settings updated successfully for ' . $page);
    }

    public function generateSitemap()
    {
        $pages = [
            ['url' => route('home'), 'priority' => '1.0'],
            ['url' => route('about'), 'priority' => '0.8'],
            ['url' => route('journey'), 'priority' => '0.8'],
            ['url' => route('journals'), 'priority' => '0.8'],
            ['url' => route('advisors'), 'priority' => '0.7'],
            ['url' => route('contact'), 'priority' => '0.6'],
        ];

        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        foreach ($pages as $page) {
            $xml .= '  <url>' . "\n";
            $xml .= '    <loc>' . $page['url'] . '</loc>' . "\n";
            $xml .= '    <lastmod>' . now()->toISOString() . '</lastmod>' . "\n";
            $xml .= '    <priority>' . $page['priority'] . '</priority>' . "\n";
            $xml .= '  </url>' . "\n";
        }

        $xml .= '</urlset>';

        file_put_contents(public_path('sitemap.xml'), $xml);

        return redirect()->route('admin.seo.index')
            ->with('success', 'Sitemap generated successfully');
    }
}
