<?php
// Include database connection and optimization functions
include 'db_connect.php';
include 'src/db_optimize.php';

// Cache function for prepared queries
function cached_prepared_query($conn, $query, $types, $params, $cache_key, $cache_time = 3600) {
    // Check if cached result exists
    $cached_result = get_cache($cache_key);
    if ($cached_result !== false) {
        return $cached_result;
    }

    // Execute the query
    $stmt = $conn->prepare($query);
    if (!$stmt) {
        throw new Exception("Prepare failed: " . $conn->error);
    }

    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }

    $stmt->execute();
    $result = $stmt->get_result();

    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = $row;
    }

    $stmt->close();

    // Cache the result
    set_cache($cache_key, $data, $cache_time);

    return $data;
}

// Cache function for simple queries
function cached_query($conn, $query, $cache_key, $cache_time = 3600) {
    // Check if cached result exists
    $cached_result = get_cache($cache_key);
    if ($cached_result !== false) {
        return $cached_result;
    }

    // Execute the query
    $result = $conn->query($query);
    if (!$result) {
        throw new Exception("Query failed: " . $conn->error);
    }

    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = $row;
    }

    // Cache the result
    set_cache($cache_key, $data, $cache_time);

    return $data;
}

$table_name = 'structure'; // Table for existing data
$id = 1; // Example: Extract data for id = 1
$data = [];
$error = '';

// Use cached prepared query for structure data
try {
  $data = cached_prepared_query(
    $conn,
    "SELECT * FROM $table_name WHERE id = ?",
    "i",
    [$id],
    'structure_data_' . $id,
    3600 // Cache for 1 hour
  );

  if (empty($data)) {
    $error = "No data found for ID $id in table '$table_name'.";
  } else {
    $data = $data[0]; // Get first row since we're querying by primary key
  }
} catch (Exception $e) {
  $error = "Error fetching data: " . $e->getMessage();
}

// Fetch data from journey table with caching
$journey_data = [];
try {
  $journey_data = cached_query(
    $conn,
    "SELECT * FROM journey ORDER BY date DESC",
    'journey_data',
    1800 // Cache for 30 minutes
  );

  if (empty($journey_data)) {
    $error .= " No data found in 'journey' table.";
  }
} catch (Exception $e) {
  $error .= " Error fetching journey data: " . $e->getMessage();
}

// Fetch data from journey_images table with caching
$journey_images = [];
try {
  $journey_images = cached_query(
    $conn,
    "SELECT * FROM journey_images ORDER BY date DESC",
    'journey_images_data',
    1800 // Cache for 30 minutes
  );

  if (empty($journey_images)) {
    $error = "No data found in 'journey_images' table.";
  }
} catch (Exception $e) {
  $error = "Error fetching journey_images data: " . $e->getMessage();
}

// Fetch data from journals&papers table with caching
$journals_papers = [];
try {
  $journals_papers = cached_query(
    $conn,
    "SELECT * FROM `journals&papers` ORDER BY id ASC",
    'journals_papers_data',
    3600 // Cache for 1 hour
  );

  if (empty($journals_papers)) {
    $error = "No data found in 'journals&papers' table.";
  }
} catch (Exception $e) {
  $error = "Error fetching journals&papers data: " . $e->getMessage();
}

// Fetch data from thesis&chapters table with caching
$thesis_chapters = [];
try {
  $thesis_chapters = cached_query(
    $conn,
    "SELECT * FROM `thesis&chapters` ORDER BY id ASC",
    'thesis_chapters_data',
    3600 // Cache for 1 hour
  );

  if (empty($thesis_chapters)) {
    $error = "No data found in 'thesis&chapters' table.";
  }
} catch (Exception $e) {
  $error = "Error fetching thesis&chapters data: " . $e->getMessage();
}

// Fetch data from advisors table with caching
$advisors = [];
try {
  $advisors = cached_query(
    $conn,
    "SELECT * FROM advisors ORDER BY id ASC",
    'advisors_data',
    7200 // Cache for 2 hours
  );

  if (empty($advisors)) {
    $error = "No data found in 'advisors' table.";
  }
} catch (Exception $e) {
  $error = "Error fetching advisors data: " . $e->getMessage();
}

// Fetch data from field_interest table with caching
$field_interests = [];
try {
  $field_interests = cached_query(
    $conn,
    "SELECT * FROM field_interest ORDER BY id DESC LIMIT 4",
    'field_interests_data',
    1800 // Cache for 30 minutes
  );
} catch (Exception $e) {
  $error .= " Error fetching field of interest data: " . $e->getMessage();
}

// Fetch PDFs for Field of Interest section with caching
$pdfs = [];
try {
  $pdfs = cached_query(
    $conn,
    "SELECT * FROM field_interest_pdfs ORDER BY upload_date DESC LIMIT 4",
    'field_interest_pdfs_data',
    1800 // Cache for 30 minutes
  );
} catch (Exception $e) {
  $error .= " Error fetching PDFs data: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Dr. Jayanta Debbarma - Groundwater Researcher in Tripura | Water Expert</title>
  <meta name="description" content="Dr. Jayanta Debbarma is a distinguished groundwater researcher from Tripura, India, specializing in groundwater potential mapping, spring identification, and sustainable water management in Tripura." />
  <meta name="keywords" content="Jayanta Debbarma, Dr. Jayanta Debbarma, groundwater Tripura, groundwater research, water management Tripura, Tripura water resources, PWD Tripura, groundwater expert, Analytical Hierarchy Process, Geographic Information Systems, springs, flood plains" />
  <meta name="author" content="Dr. Jayanta Debbarma" />

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://jayantadebbarma.tripura.cloud/" />
  <meta property="og:title" content="Dr. Jayanta Debbarma - Groundwater Researcher in Tripura | Water Expert" />
  <meta property="og:description" content="Dr. Jayanta Debbarma is a distinguished groundwater researcher from Tripura, specializing in groundwater potential mapping and sustainable water management in Tripura." />
  <meta property="og:image" content="https://jayantadebbarma.tripura.cloud/asset/images/main.png" />

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image" />
  <meta property="twitter:url" content="https://jayantadebbarma.tripura.cloud/" />
  <meta property="twitter:title" content="Dr. Jayanta Debbarma - Groundwater Researcher in Tripura | Water Expert" />
  <meta property="twitter:description" content="Dr. Jayanta Debbarma is a distinguished groundwater researcher from Tripura, specializing in groundwater potential mapping and sustainable water management in Tripura." />
  <meta property="twitter:image" content="https://jayantadebbarma.tripura.cloud/asset/images/main.png" />

  <!-- DNS prefetch for external domains -->
  <link rel="dns-prefetch" href="https://cdn.jsdelivr.net">
  <link rel="dns-prefetch" href="https://cdnjs.cloudflare.com">

  <!-- Preconnect to critical domains -->
  <link rel="preconnect" href="https://cdn.jsdelivr.net" crossorigin>
  <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>

  <!-- Favicon -->
  <link
    rel="shortcut icon"
    href="asset/images/fabicon.png"
    type="image/x-icon" />

  <!-- Preload critical assets -->
  <link rel="preload" href="asset/images/main.png" as="image" type="image/png">
  <link rel="preload" href="src/critical.css" as="style">

  <!-- Critical CSS inline -->
  <style>
    <?php include 'src/critical.css'; ?>

    /* Add responsive design rules */
    :root {
      --card-width-large: clamp(15rem, 20vw, 20rem);
      --card-height-large: clamp(18rem, 24vw, 24rem);
      --card-width-small: clamp(8rem, 10vw, 10rem);
      --card-height-small: clamp(10rem, 12vw, 12rem);
    }

    /* Desktop-specific styles */
    body {
      min-width: 1024px;
    }
  </style>

  <!-- Defer non-critical CSS -->
  <link rel="preload" href="src/output.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <noscript><link rel="stylesheet" href="src/output.css"></noscript>

  <!-- Structured Data for SEO -->
  <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Person",
      "name": "Dr. Jayanta Debbarma",
      "jobTitle": "Groundwater Researcher, Superintending Engineer PWD(W.R)",
      "description": "Distinguished groundwater researcher from Tripura, India, specializing in sustainable water management, groundwater potential mapping, and spring identification.",
      "image": "https://jayantadebbarma.tripura.cloud/asset/images/main.png",
      "url": "https://jayantadebbarma.tripura.cloud",
      "sameAs": [
        "https://scholar.google.co.in/citations?user=I4M4AewAAAAJ"
      ],
      "knowsAbout": [
        "Groundwater Research",
        "Sustainable Water Management",
        "Groundwater Potential Mapping",
        "Spring Shed Management",
        "Spring Identification",
        "Analytical Hierarchy Process",
        "Geographic Information Systems"
      ],
      "alumniOf": [{
          "@type": "CollegeOrUniversity",
          "name": "Jadavpur University",
          "location": "Kolkata, India"
        },
        {
          "@type": "CollegeOrUniversity",
          "name": "NERIST",
          "location": "India"
        }
      ],
      "workLocation": {
        "@type": "Place",
        "address": {
          "@type": "PostalAddress",
          "addressLocality": "Agartala",
          "addressRegion": "Tripura",
          "addressCountry": "India"
        }
      },
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": "https://jayantadebbarma.tripura.cloud"
      }
    }
  </script>
</head>

<body class="overflow-x-hidden">
  <!-- Navbar (Sticky) -->
  <nav
    class="w-full flex justify-between items-center px-15 h-[10vh] border-b-[1px] bg-white z-100 sticky top-0">
    <a
      id="nav-logo"
      class="font-extrabold text-4xl cursor-pointer drop-shadow-md"
      href="#home">
      <span class="text-blue-500"><?php echo htmlspecialchars($data['honorific'] ?? 'Dr'); ?></span> <?php echo htmlspecialchars($data['name'] ?? 'Jayanta Debbarma'); ?>
    </a>
    <div
      id="nav-buttons"
      class="w-[50%] flex items-center justify-around font-bold text-l">
      <a href="#home" class="cursor-pointer hover:text-blue-400"><?php echo htmlspecialchars($data['page1'] ?? 'Home'); ?></a>
      <a href="#about" class="cursor-pointer hover:text-blue-400"><?php echo htmlspecialchars($data['page2'] ?? 'About'); ?></a>
      <a href="#journey" class="cursor-pointer hover:text-blue-400"><?php echo htmlspecialchars($data['page3'] ?? 'Journey'); ?></a>
      <a href="#journals" class="cursor-pointer hover:text-blue-400"><?php echo htmlspecialchars($data['page4'] ?? 'Journals'); ?></a>
      <a href="#thesis" class="cursor-pointer hover:text-blue-400"><?php echo htmlspecialchars($data['page5'] ?? 'Thesis'); ?></a>
      <a href="#advisor" class="cursor-pointer hover:text-blue-400"><?php echo htmlspecialchars($data['page6'] ?? 'Advisors'); ?></a>
      <a href="#interest" class="cursor-pointer hover:text-blue-400"><?php echo htmlspecialchars($data['page7'] ?? 'Field of Interest'); ?></a>
      <a href="#contact" class="cursor-pointer hover:text-blue-400 transition ease-in-out"><?php echo htmlspecialchars($data['page8'] ?? 'Contact'); ?></a>
    </div>
  </nav>

  <!-- Main Content -->
  <main class="">
    <section id="home" class="relative w-full mb-20 flex flex-row">
      <div
        class="w-[60%] h-[90vh] flex flex-col items-center justify-center gap-3">
        <div class="flex flex-col items-center gap-1.5">
          <h1 class="font-extrabold text-6xl">
            <span class="text-blue-500"><?php echo htmlspecialchars($data['honorific'] ?? 'Dr'); ?></span> <?php echo htmlspecialchars($data['name'] ?? 'Jayanta Debbarma'); ?>
          </h1>
          <h5 class="font-bold"><?php echo htmlspecialchars($data['short_bio'] ?? '(Groundwater Researcher, Superintending Engineer PWD(W.R))'); ?></h5>
        </div>
        <p>
          <?php echo htmlspecialchars($data['moto'] ?? 'Dedicated to Preserving Earth\'s Groundwater for Generations to Come.'); ?>
        </p>
      </div>
      <div class="h-full w-[40%] relative">
        <!-- Blue Background Shape -->
        <div
          class="w-[30vw] h-[30vw] bg-blue-500 absolute top-10 left-10 z-0 rounded-3xl blur-3xl"
          id="blured-bg"></div>
        <!-- Image -->
        <img
          data-src="asset/images/main.png"
          src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
          class="h-[90vh] relative z-10 lazy"
          alt="Dr. Jayanta Debbarma" />
      </div>
    </section>

    <!-- About Section -->
    <section id="about" class="w-full h-[90vh] mb-[20vh] flex">
      <div class="w-[40%] h-full flex items-center justify-center">
        <div class="relative w-[30vw] h-[30vw] bg-blue-500 rounded-2xl">
          <img
            id="about-image"
            class="w-full h-full object-cover absolute top-[5%] left-[5%] rounded-2xl drop-shadow-md lazy"
            data-src="asset/images/page1.jpg"
            src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
            alt="Dr. Jayanta Debbarma" />
        </div>
      </div>
      <div class="w-[60%] px-[5%] flex flex-col justify-center gap-10">
        <h1 class="font-extrabold text-6xl">
          Meet<br /><span class="text-blue-500"><?php echo htmlspecialchars($data['honorific'] ?? 'Dr'); ?></span> <?php echo htmlspecialchars($data['name'] ?? 'Jayanta Debbarma'); ?>
        </h1>
        <p id="intro-text" class="text-xl font-semibold">
        </p>
      </div>
    </section>

    <!-- Journey Section -->
    <section id="journey" class="w-full h-[120vh] mb-[10vh] flex">
      <div class="w-[50%] h-full">
        <div
          class="w-full h-[10%] bg-blue-500 rounded-r-lg flex justify-center items-center">
          <h1 class="font-extrabold text-4xl"><?php echo htmlspecialchars($data['page3_title'] ?? 'Education & Experience'); ?></h1>
        </div>

        <div
          class="w-full h-[80%] flex flex-col justify-around items-start pl-16">
          <?php foreach ($journey_data as $journey): ?>
            <div
              class="w-[80%] py-3 bg-gray-100 flex items-center gap-8 pl-7 rounded-2xl drop-shadow-md exp-box">
              <img
                src="asset/icons/<?php echo file_exists('asset/icons/' . $journey['icon']) ? htmlspecialchars($journey['icon']) : 'default.png'; ?>"
                alt="<?php echo htmlspecialchars($journey['title'] ?? 'Icon'); ?>"
                class="w-[3vw] h-[3vw]"
                onerror="this.src='asset/icons/default.png'; this.onerror=null;" />
              <div class="flex flex-col minn-w-[50%]">
                <h2 class="text-2xl font-bold">
                  <?php echo htmlspecialchars($journey['title'] ?? 'Untitled'); ?>.
                </h2>
                <div class="flex w-full justify-between gap-5">
                  <?php if (!empty($journey['sub_title'])): ?>
                    <p class="font-bold text-blue-950">
                      <?php echo htmlspecialchars($journey['sub_title']); ?>
                    </p>
                  <?php else: ?>
                    <p class="text-gray-400 italic">No subtitle</p>
                  <?php endif; ?>
                  <p><?php echo htmlspecialchars($journey['date'] ?? 'Unknown date'); ?></p>
                </div>
              </div>
            </div>
          <?php endforeach; ?>
          <?php if (empty($journey_data)): ?>
            <p class="text-red-500 pl-7">No journey data available.</p>
          <?php endif; ?>
        </div>
      </div>

      <div class="w-[50%] h-full relative">
        <!-- Small Cards Ordered from Top to Bottom -->
        <?php
        // Define positioning and rotation for each mini-card
        $positions = [
          ['top' => '5%', 'left' => '65%', 'rotate' => '-10deg'],
          ['top' => '10%', 'left' => '20%', 'rotate' => '10deg'],
          ['top' => '40%', 'left' => '70%', 'rotate' => '15deg'],
          ['top' => '40%', 'left' => '10%', 'rotate' => '-15deg'],
          ['top' => '70%', 'left' => '20%', 'rotate' => '5deg'],
          ['top' => '72%', 'left' => '60%', 'rotate' => '-5deg']
        ];

        // Counter for assigning positions
        $index = 0;

        foreach ($journey_images as $image) {
          // Check if the image is for a mini-card or large card
          if ($image['image_for'] === 'largePic') {
            // Large card
        ?>
            <div
              class="absolute top-[25%] left-[30%] w-[var(--card-width-large)] h-[var(--card-height-large)] z-50 flex flex-col justify-center shadow-blue-500 shadow-2xl border-[1px] border-gray-100 rounded-sm bg-white">
              <div
                class="w-[80%] h-[65%] mx-auto bg-cover bg-center border-[1px] border-gray-100 rounded-sm"
                style="background-image: url('asset/images/<?php echo htmlspecialchars($image['image_name']); ?>')"></div>
              <!-- Title and date in the empty space -->
              <div class="w-[80%] h-[30%] mx-auto flex justify-around items-center">
                <div class="text-center">
                  <h3 class="text-lg font-bold text-blue-500 line-clamp-1"><?php echo htmlspecialchars($image['name'] ?? ''); ?></h3>
                  <p class="text-sm text-gray-600"><?php echo htmlspecialchars($image['date'] ?? ''); ?></p>
                </div>
              </div>
            </div>
          <?php
          } else {
            // Mini-card
            // Use position data if available, otherwise use default
            $pos = isset($positions[$index]) ? $positions[$index] : ['top' => '10%', 'left' => '10%', 'rotate' => '0deg'];
          ?>
            <div
              class="absolute bg-white z-1 top-[<?php echo $pos['top']; ?>] left-[<?php echo $pos['left']; ?>] rotate-[<?php echo $pos['rotate']; ?>] w-[var(--card-width-small)] h-[var(--card-height-small)] flex flex-col justify-center shadow-blue-500 shadow-2xl border-[1px] border-gray-100 rounded-sm box">
              <div
                class="w-[80%] h-[55%] mx-auto bg-cover bg-center border-[1px] border-gray-100 rounded-sm"
                style="background-image: url('asset/images/<?php echo htmlspecialchars($image['image_name']); ?>')"></div>
              <!-- Title and date in the empty space -->
              <div class="w-[80%] h-[35%] mx-auto flex justify-around items-center">
                <div class="text-center">
                  <h4 class="text-sm font-bold text-blue-500 line-clamp-1"><?php echo htmlspecialchars($image['name'] ?? ''); ?></h4>
                  <p class="text-xs text-gray-600"><?php echo htmlspecialchars($image['date'] ?? ''); ?></p>
                </div>
              </div>
            </div>
        <?php
            $index++; // Increment position index for the next mini-card
          }
        }
        ?>
      </div>
    </section>

    <!-- Journals Section -->
    <section id="journals" class="w-full h-[120vh] mb-[20vh] flex">
      <div class="relative w-[50%] h-[80%] overflow-hidden">
        <!-- Blurred Circle -->
        <div
          class="absolute bg-blue-500 w-[80vw] md:w-[50vw] h-[50vw] md:h-[30vw] top-[20%] left-[-20%] md:left-[-10%] rounded-full blur-xl z-0"
          id="blured-bg-journals"></div>

        <!-- Image -->
        <img
          src="asset/images/degree.png"
          alt="Degree Image"
          class="absolute w-full h-full object-contain md:object-cover z-10" />
      </div>

      <div
        class="w-[50%] h-full flex flex-col items-center justify-center gap-7">
        <h1 class="bg-blue-500 font-extrabold text-5xl px-6 py-7 rounded-2xl">
          <?php echo htmlspecialchars($data['page4_title'] ?? 'Journals & Papers'); ?>
        </h1>
        <div class="flex flex-col h-[90%] justify-around items-center w-full">
          <?php foreach ($journals_papers as $paper): ?>
            <a
              class="flex items-center space-x-3 max-w-[90%] px-5 py-2 rounded-2xl bg-gray-100 drop-shadow-md gap-2 w-full h-[8%] cursor-pointer jp-card"
              href="<?php echo htmlspecialchars($paper['link']); ?>"
              target="_blank"
            >
              <img
                src="asset/icons/<?php echo htmlspecialchars($paper['icon']); ?>"
                alt="Paper Icon"
                class="w-6 h-6"
              />
              <p class="font-semibold">
                <?php echo htmlspecialchars($paper['name']); ?>
              </p>
            </a>
          <?php endforeach; ?>
          <?php if (empty($journals_papers)): ?>
            <p class="text-red-500"><?php echo htmlspecialchars($error); ?></p>
          <?php endif; ?>
        </div>
      </div>
    </section>

    <!-- Thesis Section -->
    <section id="thesis" class="w-full h-[90vh] mb-[10vh] flex flex-col">
      <div class="w-full h-[20%] flex justify-center items-center">
        <div
          class="py-6 px-7 rounded-2xl flex justify-center items-center bg-blue-500"
          id="blue-box-thesis">
          <h1 class="text-5xl font-bold z-[10]"><?php echo htmlspecialchars($data['page5_title'] ?? 'Thesis & Chapters'); ?></h1>
        </div>
      </div>
      <div class="w-full h-[80%] flex items-center justify-center">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl w-full px-4">
          <?php foreach ($thesis_chapters as $thesis): ?>
            <a
              class="flex flex-col justify-around text-center items-center space-x-3 px-3 py-2 rounded-2xl bg-gray-100 drop-shadow-md gap-2 cursor-pointer jp-card h-[400px] relative"
              <?php if (!empty($thesis['link'])): ?>
                href="<?php echo htmlspecialchars($thesis['link']); ?>"
                target="_blank"
              <?php endif; ?>
            >
            <div class="flex flex-col justify-between items-center">
              <img
                src="asset/icons/<?php echo htmlspecialchars($thesis['icon']); ?>"
                alt="Paper Icon"
                class="w-10 h-10"
              />
              <p class="font-bold">
                <?php echo htmlspecialchars($thesis['name']); ?>
              </p>
            </div>
            <p>
              Thesis Submitted By <br /><span class="font-bold"><?php echo htmlspecialchars($thesis['sub_by_name']); ?></span>
              <?php if (!empty($thesis['sub_by_designation'])): ?>
                <br /><?php echo htmlspecialchars($thesis['sub_by_designation']); ?>
              <?php endif; ?>
            </p>
            <?php if (!empty($thesis['guide_1_name']) || !empty($thesis['guide_2_name'])): ?>
              <p>
                Under the guidance of <br />
                <?php if (!empty($thesis['guide_1_name'])): ?>
                  <span class="font-bold"><?php echo htmlspecialchars($thesis['guide_1_name']); ?></span> <br />
                  <?php echo htmlspecialchars($thesis['guide_1_designation']); ?><br />
                <?php endif; ?>
                <?php if (!empty($thesis['guide_2_name'])): ?>
                  <span class="font-bold"><?php echo htmlspecialchars($thesis['guide_2_name']); ?></span> <br />
                  <?php echo htmlspecialchars($thesis['guide_2_designation']); ?>
                <?php endif; ?>
              </p>
            <?php endif; ?>
            <p class="font-bold text-blue-950">
              <?php echo htmlspecialchars($thesis['Institution']); ?>
            </p>
          </a>
          <?php endforeach; ?>
          <?php if (empty($thesis_chapters)): ?>
            <div class="col-span-full">
              <p class="text-red-500 text-center w-full"><?php echo htmlspecialchars($error); ?></p>
            </div>
          <?php endif; ?>
        </div>
      </div>
    </section>

    <!-- Advisor's Section -->
    <section id="advisor" class="w-full h-[90vh] mb-[10vh] flex flex-col">
      <div class="w-full h-[20%] flex justify-center items-center">
        <div
          class="py-6 px-7 rounded-2xl flex justify-center items-center bg-blue-500"
          id="blue-box-advisor">
          <h1 class="text-5xl font-bold z-[10]"><?php echo htmlspecialchars($data['page6_title'] ?? 'Key Advisors'); ?></h1>
        </div>
      </div>
      <div class="w-full h-[80%] flex items-center justify-center">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl w-full px-4">
          <?php foreach ($advisors as $advisor): ?>
            <div class="w-full h-[400px] rounded-2xl flex flex-col justify-around items-center bg-gray-200 drop-shadow-2xl">
            <img
              data-src="asset/images/<?php echo htmlspecialchars($advisor['image']); ?>"
              src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
              alt="<?php echo htmlspecialchars($advisor['name']); ?>"
              class="bg-white w-full max-w-[12vw] h-auto rounded-2xl object-cover lazy"
            />
            <div class="w-[90%] text-center flex flex-col">
              <h1 class="font-bold text-2xl"><?php echo htmlspecialchars($advisor['name']); ?></h1>
              <p><?php echo htmlspecialchars($advisor['designation']); ?></p>
              <h3 class="font-bold text-xl"><?php echo htmlspecialchars($advisor['rank']); ?></h3>
              <?php if (!empty($advisor['email'])): ?>
                <h3 class="font-bold text-xl break-words">
                  <a href="mailto:<?php echo htmlspecialchars($advisor['email']); ?>">
                    <?php echo htmlspecialchars($advisor['email']); ?>
                  </a>
                </h3>
              <?php endif; ?>
            </div>
          </div>
          <?php endforeach; ?>
          <?php if (empty($advisors)): ?>
            <div class="col-span-full">
              <p class="text-red-500 text-center w-full"><?php echo htmlspecialchars($error); ?></p>
            </div>
          <?php endif; ?>
        </div>
      </div>
    </section>

    <!-- Interest Section -->
    <section id="interest" class="w-full h-[90vh] mb-[10vh] flex flex-col">
      <div class="w-full h-[20%] flex justify-center items-center">
        <div
          class="py-6 px-7 rounded-2xl flex justify-center items-center bg-blue-500"
          id="blue-box-interest">
          <h1 class="text-5xl font-bold z-[10]"><?php echo htmlspecialchars($data['page7_title'] ?? 'Field of Interest'); ?></h1>
        </div>
      </div>
      <div class="w-full h-[40%] flex border-y-1 border-gray-300">
        <div class="h-full w-[20%] flex justify-center items-center">
          <h1 class="font-bold text-4xl">PDFs <br> & <br> Documents</h1>
        </div>
        <div class="h-full w-[70%] flex justify-around items-center">
          <?php if (!empty($pdfs)): ?>
            <?php foreach (array_slice($pdfs, 0, 4) as $index => $pdf): ?>
              <div class="w-[18%] h-[90%] bg-black rounded-2xl relative overflow-hidden cursor-pointer item-hover">
                <a href="asset/pdfs/<?php echo htmlspecialchars($pdf['pdf_file']); ?>" target="_blank" class="block w-full h-full">
                  <img
                    class="w-full h-full object-cover rounded-2xl drop-shadow-md lazy"
                    data-src="asset/thumbnails/<?php echo htmlspecialchars($pdf['thumbnail']); ?>"
                    src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
                    alt="<?php echo htmlspecialchars($pdf['title']); ?>" />
                  <!-- Hover overlay with title -->
                  <div class="overlay-content">
                    <div class="text-white text-center p-2">
                      <p class="font-bold text-base"><?php echo htmlspecialchars($pdf['title']); ?></p>
                      <span class="text-sm"><?php echo date('m/d/y', strtotime($pdf['display_date'] ?? $pdf['upload_date'])); ?></span>
                    </div>
                  </div>
                </a>
              </div>
            <?php endforeach; ?>
          <?php else: ?>
            <!-- Placeholder PDFs if none are available -->
            <div class="w-[18%] h-[90%] bg-black rounded-2xl relative overflow-hidden cursor-pointer item-hover">
              <a href="#" class="block w-full h-full">
                <img
                  class="w-full h-full object-cover rounded-2xl drop-shadow-md lazy"
                  data-src="asset/images/pdf1.png"
                  src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
                  alt="Sample PDF" />
                <div class="overlay-content">
                  <div class="text-white text-center p-2">
                    <p class="font-bold text-base">Sample PDF 1</p>
                    <span class="text-sm">Click to view</span>
                  </div>
                </div>
              </a>
            </div>
            <!-- Add more placeholder PDFs as needed -->
          <?php endif; ?>
        </div>
        <div class="h-full w-[10%] flex items-center">
          <a href="field_interest_pdfs.php" class="px-4 py-3 bg-blue-500 font-bold rounded-2xl cursor-pointer hover:bg-blue-600 focus:text-white">View More</a>
        </div>
      </div>
      <div class="w-full h-[40%] border-y-1 border-gray-300 flex">
        <div class="h-full w-[20%] flex justify-center items-center">
          <h1 class="font-bold text-4xl">Images <br> & <br> Videos</h1>
        </div>
        <div class="h-full w-[70%] flex justify-around items-center">
          <?php if (!empty($media_items)): ?>
            <?php foreach (array_slice($media_items, 0, 4) as $index => $media): ?>
              <div class="w-[18%] h-[90%] bg-black rounded-2xl relative overflow-hidden cursor-pointer item-hover">
                <?php if ($media['media_type'] === 'image'): ?>
                  <a href="asset/media/<?php echo htmlspecialchars($media['file_path']); ?>" target="_blank" class="block w-full h-full">
                    <img
                      class="w-full h-full object-cover rounded-2xl drop-shadow-md lazy"
                      src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
                      data-src="asset/media/<?php echo htmlspecialchars($media['file_path']); ?>"
                      alt="<?php echo htmlspecialchars($media['title']); ?>" />
                    <!-- Hover overlay with title -->
                    <div class="overlay-content">
                      <div class="text-white text-center p-2">
                        <p class="font-bold text-base"><?php echo htmlspecialchars($media['title']); ?></p>
                        <span class="text-sm"><?php echo date('m/d/y', strtotime($media['display_date'] ?? $media['upload_date'])); ?></span>
                      </div>
                    </div>
                  </a>
                <?php else: ?>
                  <a href="asset/media/<?php echo htmlspecialchars($media['file_path']); ?>" target="_blank" class="block w-full h-full">
                    <img
                      class="w-full h-full object-cover rounded-2xl drop-shadow-md lazy"
                      src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
                      data-src="<?php echo !empty($media['thumbnail']) ? 'asset/thumbnails/' . htmlspecialchars($media['thumbnail']) : 'asset/images/video_placeholder.jpg'; ?>"
                      alt="<?php echo htmlspecialchars($media['title']); ?>" />
                    <!-- Video play button -->
                    <div class="video-play-button">
                      <div class="w-12 h-12 bg-white bg-opacity-75 rounded-full flex items-center justify-center">
                        <div class="w-0 h-0 border-t-8 border-b-8 border-l-12 border-transparent border-l-blue-500 ml-1"></div>
                      </div>
                    </div>
                    <!-- Hover overlay with title -->
                    <div class="overlay-content">
                      <div class="text-white text-center p-2">
                        <p class="font-bold text-base"><?php echo htmlspecialchars($media['title']); ?></p>
                        <span class="text-sm"><?php echo date('m/d/y', strtotime($media['display_date'] ?? $media['upload_date'])); ?></span>
                      </div>
                    </div>
                  </a>
                <?php endif; ?>
              </div>
            <?php endforeach; ?>
          <?php else: ?>
            <!-- Placeholder media items if none are available -->
            <div class="w-[18%] h-[90%] bg-black rounded-2xl relative overflow-hidden cursor-pointer item-hover">
              <a href="#" class="block w-full h-full">
                <img
                  class="w-full h-full object-cover rounded-2xl drop-shadow-md lazy"
                  data-src="asset/images/field1.jpeg"
                  src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
                  alt="Field Image" />
                <div class="overlay-content">
                  <div class="text-white text-center p-2">
                    <p class="font-bold text-base">Field Image 1</p>
                    <span class="text-sm">Click to view</span>
                  </div>
                </div>
              </a>
            </div>
            <!-- Add more placeholder media items as needed -->
          <?php endif; ?>
        </div>
        <div class="h-full w-[10%] flex items-center">
          <a href="field_interest_media.php" class="px-4 py-3 bg-blue-500 font-bold rounded-2xl cursor-pointer hover:bg-blue-600 focus:text-white">View More</a>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section
      id="contact"
      class="w-full h-[120vh] pb-[20vh] bg-gradient-to-t from-blue-500 to-white flex justify-center items-center">
      <div
        class="w-[50%] rounded-3xl shadow-2xl bg-white p-8 flex flex-col justify-center py-10">
        <h2 class="text-2xl font-bold text-center mb-6 text-gray-700">
          Send Message to <?php echo htmlspecialchars($data['honorific'] ?? 'Dr'); ?> <?php echo htmlspecialchars($data['name'] ?? 'Jayanta Debbarma'); ?>
        </h2>

        <form class="space-y-4" method="POST" action="src/send_mail.php">
          <div>
            <label class="block text-gray-600 font-medium mb-1">Name</label>
            <input
              type="text"
              name="name"
              class="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-400 outline-none"
              placeholder="Enter your name"
              required />
          </div>

          <div>
            <label class="block text-gray-600 font-medium mb-1">Email</label>
            <input
              type="email"
              name="email"
              class="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-400 outline-none"
              placeholder="Enter your email"
              required />
          </div>

          <div>
            <label class="block text-gray-600 font-medium mb-1">Phone</label>
            <input
              type="tel"
              name="phone"
              class="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-400 outline-none"
              placeholder="Enter your phone number" />
          </div>

          <div>
            <label class="block text-gray-600 font-medium mb-1">Message</label>
            <textarea
              name="message"
              class="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-400 outline-none resize-none h-24"
              placeholder="Enter your message"
              required></textarea>
          </div>

          <button
            type="submit"
            name="send"
            class="w-full bg-blue-500 text-white py-3 rounded-lg hover:bg-blue-600 transition duration-300 cursor-pointer">
            Send Message
          </button>
        </form>
      </div>
    </section>

    <!-- Footer -->
    <section class="w-full h-[90vh] bg-blue-500 flex pb-3 justify-around">
      <div class="w-[60%] h-full pl-7 flex flex-col justify-between">
        <h1 class="text-7xl font-extrabold text-white">
          Dr. Jayanta Debbarma
        </h1>
        <h1 class="text-xl font-bold text-white">
          © 2025 iLogitron Technologies (P) Ltd. All Right Reserved.
        </h1>
      </div>
      <div class="w-[40%] h-full"></div>
    </section>
  </main>

  <!-- Visitor Counter -->
  <p class="bg-blue-500 rounded-l-2xl" style="position: fixed; right: 0px; bottom: 10px; color: white; padding: 8px 20px; border: left 8px; z-index: 10000;">
    Visitors: <span id="visitorCount">Loading...</span>
  </p>

  <!-- Visitor Counter Script -->
  <script>
    // Visitor counter - load after page content
    window.addEventListener('load', function() {
      // Fetch visitor count from the new visitor counter
      fetch('visitor.php?page=home')
        .then(response => {
          if (!response.ok) {
            throw new Error('Network response was not ok');
          }
          return response.text();
        })
        .then(data => {
          // Make sure we got a valid number
          const count = parseInt(data.trim());
          if (!isNaN(count) && count >= 0) {
            document.getElementById("visitorCount").textContent = count;
          } else {
            // Fallback to 0 if invalid response
            document.getElementById("visitorCount").textContent = "0";
          }
        })
        .catch(error => {
          console.error('Error fetching visitor count:', error);
          // Set default value on error
          document.getElementById("visitorCount").textContent = "0";
        });
    });
  </script>

  <!-- Service Worker Registration -->
  <script src="src/register-sw.js" defer></script>

  <!-- Load critical scripts first -->
  <script src="src/webp_support.js" defer></script>
  <script src="src/lazy-load.js" defer></script>
  <script src="src/responsive-images.js" defer></script>

  <!-- Load GSAP with lower priority -->
  <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.5/dist/gsap.min.js" defer></script>
  <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.5/dist/ScrollTrigger.min.js" defer></script>
  <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.7/dist/TextPlugin.min.js" defer></script>
  <script src="src/script.js" defer></script>

  <!-- Responsive script -->
  <script>
    // Handle responsive scaling
    function adjustForScreenSize() {
      const width = window.innerWidth;
      const height = window.innerHeight;
      const ratio = width / height;

      // Adjust layout based on screen ratio
      if (ratio < 1.5) { // Narrower screens
        document.documentElement.classList.add('narrow-screen');
      } else {
        document.documentElement.classList.remove('narrow-screen');
      }
    }

    // Run on load and resize with passive event listener for better performance
    window.addEventListener('load', adjustForScreenSize, {passive: true});
    window.addEventListener('resize', adjustForScreenSize, {passive: true});
  </script>

  <!-- Custom styles for hover effects -->
  <style>
    .item-hover {
      position: relative;
    }

    .overlay-content {
      position: absolute;
      inset: 0;
      background-color: rgba(0, 0, 0, 0.6);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;
      border-radius: 1rem;
      z-index: 5;
    }

    .video-play-button {
      position: absolute;
      inset: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
    }

    .item-hover:hover .overlay-content {
      opacity: 1;
    }

    /* Progressive image loading styles */
    .img-blur {
      filter: blur(15px);
      transform: scale(1.05);
      transition: filter 0.5s ease-out, transform 0.5s ease-out;
      will-change: filter, transform;
    }

    .img-loaded {
      filter: blur(0);
      transform: scale(1);
    }

    /* Placeholder style for images before they load */
    img.lazy {
      background-color: #f0f0f0;
      transition: opacity 0.3s ease;
    }

    /* Text truncation for journey cards */
    .line-clamp-1 {
      display: -webkit-box;
      -webkit-line-clamp: 1;
      line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .line-clamp-2 {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  </style>
</body>
</html>
