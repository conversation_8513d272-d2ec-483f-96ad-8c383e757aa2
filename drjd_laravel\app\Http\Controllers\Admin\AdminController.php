<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Journey;
use App\Models\JournalPaper;
use App\Models\Advisor;
use App\Models\PageView;

class AdminController extends Controller
{
    public function showLogin()
    {
        if (Auth::check()) {
            return redirect()->route('admin.dashboard');
        }

        return view('admin.login');
    }

    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        $credentials = $request->only('email', 'password');

        if (Auth::attempt($credentials, $request->filled('remember'))) {
            $request->session()->regenerate();
            return redirect()->intended(route('admin.dashboard'));
        }

        return back()->withErrors([
            'email' => 'The provided credentials do not match our records.',
        ])->onlyInput('email');
    }

    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('admin.login');
    }

    public function dashboard()
    {
        $stats = [
            'total_journeys' => Journey::count(),
            'total_journals' => JournalPaper::count(),
            'total_advisors' => Advisor::count(),
            'total_page_views' => PageView::getTotalViews(),
            'home_page_views' => PageView::getCount('home'),
            'about_page_views' => PageView::getCount('about'),
            'journey_page_views' => PageView::getCount('journey'),
            'journals_page_views' => PageView::getCount('journals'),
        ];

        return view('admin.dashboard', compact('stats'));
    }

    public function createAdmin(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'city' => 'nullable|string|max:255',
        ]);

        User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'city' => $request->city,
        ]);

        return redirect()->route('admin.dashboard')
            ->with('success', 'Admin account created successfully');
    }
}
