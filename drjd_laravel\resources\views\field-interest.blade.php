<x-layout.app page="field-interest">
    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-orange-600 to-red-600 text-white py-16">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto text-center">
                <h1 class="text-4xl md:text-5xl font-bold mb-6">{{ $structure->page7_title ?? 'Field of Interest' }}</h1>
                <p class="text-xl">Research areas, documents, and multimedia content</p>
            </div>
        </div>
    </section>

    <!-- Field of Interest Overview -->
    @if($fieldInterests->count() > 0)
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="max-w-6xl mx-auto">
                <h2 class="text-3xl font-bold text-center text-gray-800 mb-12">Research Areas</h2>

                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    @foreach($fieldInterests as $field)
                        <div class="bg-white rounded-lg shadow-lg hover:shadow-xl transition duration-300 overflow-hidden">
                            @if($field->image)
                                <img src="{{ asset('asset/images/' . $field->image) }}"
                                     alt="{{ $field->title }}"
                                     class="w-full h-48 object-cover">
                            @else
                                <div class="w-full h-48 bg-orange-100 flex items-center justify-center">
                                    <i class="fas fa-lightbulb text-orange-600 text-4xl"></i>
                                </div>
                            @endif

                            <div class="p-6">
                                <div class="flex items-center mb-4">
                                    @if($field->icon)
                                        <img src="{{ asset('asset/icons/' . $field->icon) }}" alt="Icon" class="w-8 h-8 mr-3">
                                    @endif
                                    <h3 class="text-xl font-bold text-gray-800">{{ $field->title }}</h3>
                                </div>

                                <p class="text-gray-600 leading-relaxed">{{ $field->description }}</p>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </section>
    @endif

    <!-- PDFs & Documents Section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="max-w-6xl mx-auto">
                <div class="flex items-center justify-between mb-12">
                    <h2 class="text-3xl font-bold text-gray-800">PDFs & Documents</h2>
                    @if($pdfs->count() > 4)
                        <a href="{{ route('field-interest.pdfs-more') }}"
                           class="text-indigo-600 hover:text-indigo-800 font-medium">
                            View More <i class="fas fa-arrow-right ml-1"></i>
                        </a>
                    @endif
                </div>

                @if($homePdfs->count() > 0)
                    <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                        @foreach($homePdfs as $pdf)
                            <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition duration-300 cursor-pointer"
                                 onclick="window.open('{{ asset('asset/pdfs/' . $pdf->pdf_file) }}', '_blank')">
                                @if($pdf->thumbnail)
                                    <img src="{{ asset('asset/images/' . $pdf->thumbnail) }}"
                                         alt="{{ $pdf->title }}"
                                         class="w-full h-32 object-cover">
                                @else
                                    <div class="w-full h-32 bg-red-100 flex items-center justify-center">
                                        <i class="fas fa-file-pdf text-red-600 text-3xl"></i>
                                    </div>
                                @endif
                                <div class="p-4">
                                    <h3 class="text-sm font-bold text-gray-900 mb-2 line-clamp-2">{{ $pdf->title }}</h3>
                                    <div class="text-xs text-gray-500">
                                        <i class="fas fa-calendar mr-1"></i>
                                        {{ $pdf->display_date->format('M Y') }}
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-12">
                        <i class="fas fa-file-pdf text-4xl text-gray-300 mb-4"></i>
                        <h3 class="text-xl font-bold text-gray-600 mb-2">No Documents Available</h3>
                        <p class="text-gray-500">PDF documents will be displayed here once they are uploaded.</p>
                    </div>
                @endif
            </div>
        </div>
    </section>

    <!-- Images & Videos Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="max-w-6xl mx-auto">
                <h2 class="text-3xl font-bold text-center text-gray-800 mb-12">Images & Videos</h2>

                @if($media->count() > 0)
                    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach($media as $item)
                            <div class="bg-white rounded-lg shadow hover:shadow-lg transition duration-300 overflow-hidden">
                                @if($item->type === 'image')
                                    <img src="{{ asset('asset/media/' . $item->file_path) }}"
                                         alt="{{ $item->title }}"
                                         class="w-full h-48 object-cover cursor-pointer hover:opacity-90 transition duration-300"
                                         onclick="openModal('{{ asset('asset/media/' . $item->file_path) }}', '{{ $item->title }}', 'image')">
                                @else
                                    <div class="w-full h-48 bg-gray-200 flex items-center justify-center cursor-pointer hover:bg-gray-300 transition duration-300"
                                         onclick="openModal('{{ asset('asset/media/' . $item->file_path) }}', '{{ $item->title }}', 'video')">
                                        <i class="fas fa-play-circle text-4xl text-gray-600"></i>
                                    </div>
                                @endif

                                <div class="p-4">
                                    <h3 class="text-lg font-semibold text-gray-800 mb-2 line-clamp-2">{{ $item->title }}</h3>
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm text-gray-500">{{ $item->date->format('M d, Y') }}</span>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $item->type === 'image' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800' }}">
                                            <i class="fas fa-{{ $item->type === 'image' ? 'image' : 'video' }} mr-1"></i>
                                            {{ ucfirst($item->type) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-12">
                        <i class="fas fa-photo-video text-4xl text-gray-300 mb-4"></i>
                        <h3 class="text-xl font-bold text-gray-600 mb-2">No Media Available</h3>
                        <p class="text-gray-500">Images and videos will be displayed here once they are uploaded.</p>
                    </div>
                @endif
            </div>
        </div>
    </section>

    <!-- Research Statistics -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <h2 class="text-3xl font-bold text-center text-gray-800 mb-12">Research Portfolio</h2>

                <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                    <div class="text-center p-6 bg-white rounded-lg shadow">
                        <div class="text-3xl font-bold text-orange-600 mb-2">{{ $fieldInterests->count() }}</div>
                        <div class="text-gray-600">Research Areas</div>
                    </div>
                    <div class="text-center p-6 bg-white rounded-lg shadow">
                        <div class="text-3xl font-bold text-red-600 mb-2">{{ $pdfs->count() }}</div>
                        <div class="text-gray-600">Documents</div>
                    </div>
                    <div class="text-center p-6 bg-white rounded-lg shadow">
                        <div class="text-3xl font-bold text-green-600 mb-2">{{ $media->where('type', 'image')->count() }}</div>
                        <div class="text-gray-600">Images</div>
                    </div>
                    <div class="text-center p-6 bg-white rounded-lg shadow">
                        <div class="text-3xl font-bold text-blue-600 mb-2">{{ $media->where('type', 'video')->count() }}</div>
                        <div class="text-gray-600">Videos</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Media Modal -->
    <div id="mediaModal" class="fixed inset-0 bg-black bg-opacity-75 hidden z-50 flex items-center justify-center p-4">
        <div class="max-w-4xl max-h-full bg-white rounded-lg overflow-hidden">
            <div class="flex justify-between items-center p-4 border-b">
                <h3 id="modalTitle" class="text-lg font-semibold text-gray-800"></h3>
                <button onclick="closeModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="modalContent" class="p-4"></div>
        </div>
    </div>

    @push('scripts')
    <script>
        function openModal(src, title, type) {
            const modal = document.getElementById('mediaModal');
            const modalTitle = document.getElementById('modalTitle');
            const modalContent = document.getElementById('modalContent');

            modalTitle.textContent = title;

            if (type === 'image') {
                modalContent.innerHTML = `<img src="${src}" alt="${title}" class="max-w-full max-h-96 mx-auto">`;
            } else {
                modalContent.innerHTML = `<video controls class="max-w-full max-h-96 mx-auto"><source src="${src}" type="video/mp4">Your browser does not support the video tag.</video>`;
            }

            modal.classList.remove('hidden');
        }

        function closeModal() {
            const modal = document.getElementById('mediaModal');
            modal.classList.add('hidden');
        }

        // Close modal when clicking outside
        document.getElementById('mediaModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
    @endpush
</x-layout.app>
