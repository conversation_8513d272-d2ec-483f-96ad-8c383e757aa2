<x-layout.admin title="Edit PDF/Document">
    <div class="mb-8">
        <div class="flex items-center space-x-4">
            <a href="{{ route('admin.field-interest-pdf.index') }}" class="text-gray-600 hover:text-gray-800">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Edit PDF/Document</h1>
                <p class="text-gray-600">Update document information</p>
            </div>
        </div>
    </div>

    <form method="POST" action="{{ route('admin.field-interest-pdf.update', $fieldInterestPdf) }}" class="space-y-6">
        @csrf
        @method('PUT')
        
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-6">Document Details</h2>
            
            <div class="grid gap-6">
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Title *</label>
                    <input type="text" id="title" name="title" required
                           value="{{ old('title', $fieldInterestPdf->title) }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                           placeholder="Enter document title">
                    @error('title')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea id="description" name="description" rows="3"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                              placeholder="Brief description of the document">{{ old('description', $fieldInterestPdf->description) }}</textarea>
                    @error('description')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <label for="pdf_file" class="block text-sm font-medium text-gray-700 mb-2">PDF Filename *</label>
                        <input type="text" id="pdf_file" name="pdf_file" required
                               value="{{ old('pdf_file', $fieldInterestPdf->pdf_file) }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                               placeholder="e.g., research-paper.pdf">
                        <p class="text-sm text-gray-500 mt-1">Upload the PDF file to /public/asset/pdfs/ directory first</p>
                        @error('pdf_file')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label for="thumbnail" class="block text-sm font-medium text-gray-700 mb-2">Thumbnail Image</label>
                        <input type="text" id="thumbnail" name="thumbnail"
                               value="{{ old('thumbnail', $fieldInterestPdf->thumbnail) }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                               placeholder="e.g., thumbnail.jpg">
                        <p class="text-sm text-gray-500 mt-1">Upload the thumbnail to /public/asset/images/ directory first</p>
                        @error('thumbnail')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
                
                <div>
                    <label for="display_date" class="block text-sm font-medium text-gray-700 mb-2">Display Date *</label>
                    <input type="date" id="display_date" name="display_date" required
                           value="{{ old('display_date', $fieldInterestPdf->display_date->format('Y-m-d')) }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                    <p class="text-sm text-gray-500 mt-1">This date will be used for sorting and display</p>
                    @error('display_date')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div class="flex items-center">
                    <input type="checkbox" id="show_on_home" name="show_on_home" value="1"
                           {{ old('show_on_home', $fieldInterestPdf->show_on_home) ? 'checked' : '' }}
                           class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                    <label for="show_on_home" class="ml-2 block text-sm text-gray-700">
                        Show on home page (maximum 4 items will be displayed)
                    </label>
                </div>
                
                @if($fieldInterestPdf->thumbnail)
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Current Thumbnail</label>
                        <div class="flex items-center space-x-4">
                            <img src="{{ asset('asset/images/' . $fieldInterestPdf->thumbnail) }}" 
                                 alt="Current Thumbnail" class="h-20 w-20 rounded-lg object-cover border"
                                 onerror="this.src='{{ asset('asset/images/default-pdf.png') }}'">
                            <div class="text-sm text-gray-600">
                                <p>Filename: {{ $fieldInterestPdf->thumbnail }}</p>
                                <p>To change, upload a new file and update the filename above</p>
                            </div>
                        </div>
                    </div>
                @endif
                
                @if($fieldInterestPdf->pdf_file)
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Current PDF File</label>
                        <div class="flex items-center space-x-4">
                            <div class="h-16 w-16 bg-red-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-file-pdf text-red-600 text-2xl"></i>
                            </div>
                            <div class="text-sm text-gray-600">
                                <p>Filename: {{ $fieldInterestPdf->pdf_file }}</p>
                                <a href="{{ asset('asset/pdfs/' . $fieldInterestPdf->pdf_file) }}" target="_blank" 
                                   class="text-indigo-600 hover:text-indigo-800">
                                    <i class="fas fa-external-link-alt mr-1"></i>
                                    View Current PDF
                                </a>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex items-center justify-between">
            <a href="{{ route('admin.field-interest-pdf.index') }}" 
               class="bg-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-400 transition duration-300">
                Cancel
            </a>
            
            <button type="submit" 
                    class="bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition duration-300">
                <i class="fas fa-save mr-2"></i>
                Update PDF/Document
            </button>
        </div>
    </form>
</x-layout.admin>
