<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Journey;

class JourneyController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $journeys = Journey::orderBy('id', 'desc')->get();
        return view('admin.journey.index', compact('journeys'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.journey.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'sub_title' => 'nullable|string|max:500',
            'date' => 'required|string|max:100',
            'icon' => 'nullable|string|max:255',
        ]);

        Journey::create($request->all());

        return redirect()->route('admin.journey.index')
            ->with('success', 'Journey item created successfully');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Journey $journey)
    {
        return view('admin.journey.edit', compact('journey'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Journey $journey)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'sub_title' => 'nullable|string|max:500',
            'date' => 'required|string|max:100',
            'icon' => 'nullable|string|max:255',
        ]);

        $journey->update($request->all());

        return redirect()->route('admin.journey.index')
            ->with('success', 'Journey item updated successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Journey $journey)
    {
        $journey->delete();

        return redirect()->route('admin.journey.index')
            ->with('success', 'Journey item deleted successfully');
    }
}
