<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('thesis_chapters', function (Blueprint $table) {
            $table->id();
            $table->string('icon');
            $table->string('name');
            $table->string('sub_by_name');
            $table->string('sub_by_designation');
            $table->string('guide_1_name');
            $table->string('guide_1_designation');
            $table->string('guide_2_name')->nullable();
            $table->string('guide_2_designation')->nullable();
            $table->string('institution');
            $table->string('link');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('thesis_chapters');
    }
};
