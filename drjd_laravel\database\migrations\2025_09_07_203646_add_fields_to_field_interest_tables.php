<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('field_interest_pdfs', function (Blueprint $table) {
            $table->string('thumbnail')->nullable()->after('file_path');
            $table->text('description')->nullable()->after('thumbnail');
            $table->boolean('show_on_home')->default(false)->after('description');
        });

        Schema::table('field_interest_media', function (Blueprint $table) {
            $table->text('description')->nullable()->after('type');
            $table->boolean('show_on_home')->default(false)->after('description');
        });

        Schema::table('thesis_chapters', function (Blueprint $table) {
            $table->string('pdf_file')->nullable()->after('link');
            $table->text('description')->nullable()->after('pdf_file');
            $table->date('date')->nullable()->after('description');
            $table->boolean('show_on_home')->default(false)->after('date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('field_interest_pdfs', function (Blueprint $table) {
            $table->dropColumn(['thumbnail', 'description', 'show_on_home']);
        });

        Schema::table('field_interest_media', function (Blueprint $table) {
            $table->dropColumn(['description', 'show_on_home']);
        });

        Schema::table('thesis_chapters', function (Blueprint $table) {
            $table->dropColumn(['pdf_file', 'description', 'date', 'show_on_home']);
        });
    }
};
