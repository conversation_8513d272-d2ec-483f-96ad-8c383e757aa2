<?php

namespace App\Services;

use App\Models\Structure;
use App\Models\Journey;
use App\Models\JournalPaper;
use App\Models\Advisor;

class SeoService
{
    public function generatePersonStructuredData()
    {
        $structure = Structure::first();
        
        if (!$structure) {
            return null;
        }
        
        return [
            "@context" => "https://schema.org",
            "@type" => "Person",
            "name" => ($structure->honorific ?? 'Dr.') . ' ' . ($structure->name ?? 'Jayanta Debbarma'),
            "jobTitle" => $structure->short_bio ?? 'Academic Professional',
            "description" => $structure->about ?? 'Academic professional dedicated to research and education',
            "url" => url('/'),
            "sameAs" => [
                // Add social media URLs here if available
            ]
        ];
    }
    
    public function generateEducationalOrganizationData()
    {
        $structure = Structure::first();
        
        return [
            "@context" => "https://schema.org",
            "@type" => "EducationalOrganization",
            "name" => config('app.name'),
            "description" => "Academic portfolio and research information",
            "url" => url('/'),
            "member" => [
                "@type" => "Person",
                "name" => ($structure->honorific ?? 'Dr.') . ' ' . ($structure->name ?? 'Jayanta <PERSON>bbarma')
            ]
        ];
    }
    
    public function generateArticleStructuredData($article)
    {
        $structure = Structure::first();
        
        return [
            "@context" => "https://schema.org",
            "@type" => "ScholarlyArticle",
            "headline" => $article->name,
            "url" => $article->link,
            "author" => [
                "@type" => "Person",
                "name" => ($structure->honorific ?? 'Dr.') . ' ' . ($structure->name ?? 'Jayanta Debbarma')
            ],
            "publisher" => [
                "@type" => "Organization",
                "name" => "Academic Journal"
            ],
            "datePublished" => $article->created_at->toISOString()
        ];
    }
    
    public function generateBreadcrumbStructuredData($breadcrumbs)
    {
        $items = [];
        
        foreach ($breadcrumbs as $index => $breadcrumb) {
            $items[] = [
                "@type" => "ListItem",
                "position" => $index + 1,
                "name" => $breadcrumb['name'],
                "item" => $breadcrumb['url']
            ];
        }
        
        return [
            "@context" => "https://schema.org",
            "@type" => "BreadcrumbList",
            "itemListElement" => $items
        ];
    }
    
    public function generateWebsiteStructuredData()
    {
        $structure = Structure::first();
        
        return [
            "@context" => "https://schema.org",
            "@type" => "Website",
            "name" => config('app.name'),
            "description" => "Academic portfolio of " . ($structure->honorific ?? 'Dr.') . ' ' . ($structure->name ?? 'Jayanta Debbarma'),
            "url" => url('/'),
            "potentialAction" => [
                "@type" => "SearchAction",
                "target" => url('/') . "?search={search_term_string}",
                "query-input" => "required name=search_term_string"
            ]
        ];
    }
}
