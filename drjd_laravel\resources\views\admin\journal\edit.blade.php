<x-layout.admin title="Edit Journal Paper">
    <div class="mb-8">
        <div class="flex items-center space-x-4">
            <a href="{{ route('admin.journal.index') }}" class="text-gray-600 hover:text-gray-800">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Edit Journal Paper</h1>
                <p class="text-gray-600">Update this journal or research paper</p>
            </div>
        </div>
    </div>

    <form method="POST" action="{{ route('admin.journal.update', $journal) }}" class="space-y-6">
        @csrf
        @method('PUT')
        
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-6">Journal Paper Details</h2>
            
            <div class="grid gap-6">
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Paper Name *</label>
                    <textarea id="name" name="name" rows="3" required
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                              placeholder="Enter the full title of your journal paper or research publication">{{ old('name', $journal->name) }}</textarea>
                    @error('name')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="link" class="block text-sm font-medium text-gray-700 mb-2">Publication Link *</label>
                    <input type="url" id="link" name="link" required
                           value="{{ old('link', $journal->link) }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                           placeholder="https://example.com/your-paper">
                    <p class="text-sm text-gray-500 mt-1">Enter the URL where the paper can be accessed</p>
                    @error('link')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="icon" class="block text-sm font-medium text-gray-700 mb-2">Icon Filename</label>
                    <input type="text" id="icon" name="icon"
                           value="{{ old('icon', $journal->icon) }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                           placeholder="e.g., journal1.png">
                    <p class="text-sm text-gray-500 mt-1">Upload the icon file to /public/asset/images/ directory first</p>
                    @error('icon')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                @if($journal->icon)
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Current Icon</label>
                        <div class="flex items-center space-x-4">
                            <img src="{{ asset('asset/images/' . $journal->icon) }}" 
                                 alt="Current Icon" class="h-16 w-16 rounded-lg object-cover border"
                                 onerror="this.src='{{ asset('asset/images/default-icon.png') }}'">
                            <div class="text-sm text-gray-600">
                                <p>Filename: {{ $journal->icon }}</p>
                                <p>To change, upload a new file and update the filename above</p>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex items-center justify-between">
            <a href="{{ route('admin.journal.index') }}" 
               class="bg-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-400 transition duration-300">
                Cancel
            </a>
            
            <button type="submit" 
                    class="bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition duration-300">
                <i class="fas fa-save mr-2"></i>
                Update Journal Paper
            </button>
        </div>
    </form>
</x-layout.admin>
