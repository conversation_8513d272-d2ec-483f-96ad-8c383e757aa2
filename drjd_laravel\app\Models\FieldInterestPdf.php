<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class FieldInterestPdf extends Model
{
    protected $fillable = [
        'title',
        'description',
        'thumbnail',
        'pdf_file',
        'upload_date',
        'display_date',
        'show_on_home',
    ];

    protected $casts = [
        'upload_date' => 'date',
        'display_date' => 'date',
        'show_on_home' => 'boolean',
    ];

    public static function getHomeItems($limit = 4)
    {
        return self::where('show_on_home', true)
                   ->orderBy('display_date', 'desc')
                   ->limit($limit)
                   ->get();
    }

    public static function getOtherItems()
    {
        return self::where('show_on_home', false)
                   ->orderBy('display_date', 'desc')
                   ->get();
    }
}
