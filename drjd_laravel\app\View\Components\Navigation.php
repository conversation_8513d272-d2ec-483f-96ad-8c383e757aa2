<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;
use App\Models\Structure;

class Navigation extends Component
{
    public $structure;

    /**
     * Create a new component instance.
     */
    public function __construct()
    {
        $this->structure = Structure::first();
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.navigation');
    }
}
