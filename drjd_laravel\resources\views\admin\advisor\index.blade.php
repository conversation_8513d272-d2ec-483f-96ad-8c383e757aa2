<x-layout.admin title="Advisor Management">
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Advisor Management</h1>
                <p class="text-gray-600">Manage your academic advisors and mentors</p>
            </div>
            <a href="{{ route('admin.advisor.create') }}" 
               class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition duration-300">
                <i class="fas fa-plus mr-2"></i>
                Add Advisor
            </a>
        </div>
    </div>

    @if($advisors->count() > 0)
        <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            @foreach($advisors as $advisor)
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center space-x-4 mb-4">
                        @if($advisor->image)
                            <img src="{{ asset('asset/images/' . $advisor->image) }}" 
                                 alt="{{ $advisor->name }}" class="h-16 w-16 rounded-full object-cover"
                                 onerror="this.src='{{ asset('asset/images/default-avatar.png') }}'">
                        @else
                            <div class="h-16 w-16 bg-gray-200 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-gray-400 text-xl"></i>
                            </div>
                        @endif
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold text-gray-900">{{ $advisor->name }}</h3>
                            <p class="text-sm text-gray-600">{{ $advisor->designation }}</p>
                        </div>
                    </div>
                    
                    <div class="space-y-2 mb-4">
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-star mr-2 text-yellow-500"></i>
                            <span>{{ $advisor->rank }}</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-envelope mr-2"></i>
                            <a href="mailto:{{ $advisor->email }}" class="text-indigo-600 hover:text-indigo-800">
                                {{ $advisor->email }}
                            </a>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-end space-x-2">
                        <a href="{{ route('admin.advisor.edit', $advisor) }}" 
                           class="text-indigo-600 hover:text-indigo-900 p-2">
                            <i class="fas fa-edit"></i>
                        </a>
                        <form method="POST" action="{{ route('admin.advisor.destroy', $advisor) }}" 
                              class="inline" onsubmit="return confirm('Are you sure you want to delete this advisor?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="text-red-600 hover:text-red-900 p-2">
                                <i class="fas fa-trash"></i>
                            </button>
                        </form>
                    </div>
                </div>
            @endforeach
        </div>
    @else
        <div class="bg-white rounded-lg shadow p-6 text-center">
            <div class="text-gray-500 mb-4">
                <i class="fas fa-users text-4xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Advisors Found</h3>
            <p class="text-gray-600 mb-4">Start adding your academic advisors and mentors.</p>
            <a href="{{ route('admin.advisor.create') }}" 
               class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition duration-300">
                <i class="fas fa-plus mr-2"></i>
                Add First Advisor
            </a>
        </div>
    @endif
</x-layout.admin>
