<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add missing fields to field_interest_media
        Schema::table('field_interest_media', function (Blueprint $table) {
            $table->boolean('show_on_home')->default(false)->after('display_date');
        });

        // Add missing fields to thesis&chapters
        Schema::table('thesis&chapters', function (Blueprint $table) {
            $table->string('pdf_file')->nullable()->after('link');
            $table->text('description')->nullable()->after('pdf_file');
            $table->date('date')->nullable()->after('description');
            $table->boolean('show_on_home')->default(false)->after('date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('field_interest_media', function (Blueprint $table) {
            $table->dropColumn('show_on_home');
        });

        Schema::table('thesis&chapters', function (Blueprint $table) {
            $table->dropColumn(['pdf_file', 'description', 'date', 'show_on_home']);
        });
    }
};
