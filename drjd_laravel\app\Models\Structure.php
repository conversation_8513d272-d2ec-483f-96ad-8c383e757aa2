<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Structure extends Model
{
    protected $table = 'structure';
    public $timestamps = false;

    protected $fillable = [
        'honorific',
        'name',
        'page1',
        'page2',
        'page3',
        'page4',
        'page5',
        'page6',
        'page7',
        'page8',
        'short_bio',
        'moto',
        'about',
        'page3_title',
        'page4_title',
        'page5_title',
        'page6_title',
        'page7_title',
    ];
}
