<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Structure;

class StructureController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $structure = Structure::first();
        return view('admin.structure.index', compact('structure'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit()
    {
        $structure = Structure::first();
        if (!$structure) {
            $structure = new Structure();
        }
        return view('admin.structure.edit', compact('structure'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request)
    {
        $request->validate([
            'honorific' => 'required|string|max:10',
            'name' => 'required|string|max:255',
            'page1' => 'required|string|max:50',
            'page2' => 'required|string|max:50',
            'page3' => 'required|string|max:50',
            'page4' => 'required|string|max:50',
            'page5' => 'required|string|max:50',
            'page6' => 'required|string|max:50',
            'page7' => 'required|string|max:50',
            'page8' => 'required|string|max:50',
            'short_bio' => 'required|string|max:500',
            'moto' => 'required|string|max:500',
            'about' => 'required|string',
            'page3_title' => 'required|string|max:100',
            'page4_title' => 'required|string|max:100',
            'page5_title' => 'required|string|max:100',
            'page6_title' => 'required|string|max:100',
            'page7_title' => 'required|string|max:100',
        ]);

        $structure = Structure::first();
        if ($structure) {
            $structure->update($request->all());
        } else {
            Structure::create($request->all());
        }

        return redirect()->route('admin.structure.index')
            ->with('success', 'Site structure updated successfully');
    }
}
