<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dr. <PERSON><PERSON> - Groundwater Researcher in Tripura | Water Expert</title>
    <meta name="description" content="Dr. <PERSON> is a distinguished groundwater researcher from Tripura, India, specializing in groundwater potential mapping, spring identification, and sustainable water management in Tripura." />
    <meta name="keywords" content="<PERSON><PERSON>, Dr. <PERSON>, groundwater Tripura, groundwater research, water management Tripura, Tripura water resources, PWD Tripura, groundwater expert, Analytical Hierarchy Process, Geographic Information Systems, springs, flood plains" />
    <meta name="author" content="Dr. Jayanta <PERSON>" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://jayantadebbarma.tripura.cloud/" />
    <meta property="og:title" content="Dr. <PERSON><PERSON> - Groundwater Researcher in Tripura | Water Expert" />
    <meta property="og:description" content="Dr. <PERSON><PERSON> is a distinguished groundwater researcher from Tripura, specializing in groundwater potential mapping and sustainable water management in Tripura." />
    <meta property="og:image" content="https://jayantadebbarma.tripura.cloud/asset/images/main.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://jayantadebbarma.tripura.cloud/" />
    <meta property="twitter:title" content="Dr. Jayanta Debbarma - Groundwater Researcher in Tripura | Water Expert" />
    <meta property="twitter:description" content="Dr. Jayanta Debbarma is a distinguished groundwater researcher from Tripura, specializing in groundwater potential mapping and sustainable water management in Tripura." />
    <meta property="twitter:image" content="https://jayantadebbarma.tripura.cloud/asset/images/main.png" />

    <!-- Favicon -->
    <link
      rel="shortcut icon"
      href="asset/images/fabicon.png"
      type="image/x-icon"
    />

    <!-- Preload critical CSS -->
    <link rel="preload" href="src/output.css" as="style" />
    <link href="src/output.css" rel="stylesheet" />

    <style>
      /* Smooth scrolling effect */
      html {
        scroll-behavior: smooth;
      }

      section {
        scroll-margin-top: 10vh;
      }

      /* Add responsive design rules */
      :root {
        --card-width-large: clamp(15rem, 20vw, 20rem);
        --card-height-large: clamp(18rem, 24vw, 24rem);
        --card-width-small: clamp(8rem, 10vw, 10rem);
        --card-height-small: clamp(10rem, 12vw, 12rem);
      }

      /* Responsive section heights */
      section {
        min-height: 90vh;
        scroll-margin-top: 10vh;
      }

      /* Media queries for different screen sizes */
      @media (max-width: 1280px) {
        html {
          font-size: 14px;
        }
        
        .responsive-heading {
          font-size: clamp(2rem, 5vw, 3rem) !important;
        }
      }

      /* For very small screens */
      @media (max-width: 768px) {
        html {
          font-size: 12px;
        }
      }
    </style>

    <!-- Structured Data for SEO -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Person",
        "name": "Dr. Jayanta Debbarma",
        "jobTitle": "Groundwater Researcher, Superintending Engineer PWD(W.R)",
        "description": "Distinguished groundwater researcher from Tripura, India, specializing in sustainable water management, groundwater potential mapping, and spring identification.",
        "image": "https://jayantadebbarma.tripura.cloud/asset/images/main.png",
        "url": "https://jayantadebbarma.tripura.cloud",
        "sameAs": [
          "https://scholar.google.co.in/citations?user=I4M4AewAAAAJ"
        ],
        "knowsAbout": [
          "Groundwater Research",
          "Sustainable Water Management",
          "Groundwater Potential Mapping",
          "Spring Shed Management",
          "Spring Identification",
          "Analytical Hierarchy Process",
          "Geographic Information Systems"
        ],
        "alumniOf": [
          {
            "@type": "CollegeOrUniversity",
            "name": "Jadavpur University",
            "location": "Kolkata, India"
          },
          {
            "@type": "CollegeOrUniversity",
            "name": "NERIST",
            "location": "India"
          }
        ],
        "workLocation": {
          "@type": "Place",
          "address": {
            "@type": "PostalAddress",
            "addressLocality": "Agartala",
            "addressRegion": "Tripura",
            "addressCountry": "India"
          }
        },
        "mainEntityOfPage": {
          "@type": "WebPage",
          "@id": "https://jayantadebbarma.tripura.cloud"
        }
      }
    </script>
  </head>

  <body class="overflow-x-hidden">
    <!-- Navbar (Sticky) -->
    <nav
      class="w-full flex justify-between items-center px-15 h-[10vh] border-b-[1px] bg-white z-100 sticky top-0"
    >
      <a
        id="nav-logo"
        class="font-extrabold text-4xl cursor-pointer drop-shadow-md"
        href="#home"
      >
        <span class="text-blue-500">Dr</span>.Jayanta Debbarma.
      </a>
      <div
        id="nav-buttons"
        class="w-[50%] flex items-center justify-around font-bold text-l"
      >
        <a href="#home" class="cursor-pointer hover:text-blue-400">Home</a>
        <a href="#about" class="cursor-pointer hover:text-blue-400">About</a>
        <a href="#journey" class="cursor-pointer hover:text-blue-400">Journey</a>
        <a href="#journals" class="cursor-pointer hover:text-blue-400">Journals</a>
        <a href="#thesis" class="cursor-pointer hover:text-blue-400">Thesis</a>
        <a href="#advisor" class="cursor-pointer hover:text-blue-400">Advisor's</a>
        <a href="#interest" class="cursor-pointer hover:text-blue-400">Field of Interest</a>
        <a href="#contact" class="cursor-pointer hover:text-blue-400 transition ease-in-out">Contact</a>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="">
      <section id="home" class="relative w-full mb-20 flex flex-col md:flex-row ">
        <div
          class="w-[60%] h-[90vh] flex flex-col items-center justify-center gap-3 "
        >
          <div class="flex flex-col items-center gap-1.5">
            <h1 class="font-extrabold text-6xl">
              <span class="text-blue-500">Dr</span>.Jayanta Debbarma.
            </h1>
            <h5 class="font-bold">(Groundwater Researcher, Superintending Engineer PWD(W.R))</h5>
          </div>
          <p>
            Dedicated to Preserving Earth's Groundwater for Generations to Come.
          </p>
        </div>
        <div class="h-full w-[40%] relative">
          <!-- Blue Background Shape -->
          <div
            class="w-[30vw] h-[30vw] bg-blue-500 absolute top-10 left-10 z-0 rounded-3xl blur-3xl"
            id="blured-bg"
          ></div>
          <!-- Image -->
          <img
            data-src="asset/images/main.png"
            src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
            class="h-[90vh] relative z-10 lazy"
            alt="Dr.Jayanta Debbarma"
          />
        </div>
      </section>

      <!-- About Section -->
      <section id="about" class="w-full h-[90vh] mb-[20vh] flex">
        <div class="w-[40%] h-full flex items-center justify-center">
          <div class="relative w-[30vw] h-[30vw] bg-blue-500 rounded-2xl">
            <img
              id="about-image"
              class="w-full h-full object-cover absolute top-[5%] left-[5%] rounded-2xl drop-shadow-md lazy"
              data-src="asset/images/page1.jpg"
              src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
              alt="Dr. Jayanta Debbarma"
            />
          </div>
        </div>
        <div class="w-[60%] px-[5%] flex flex-col justify-center gap-10">
          <h1 class="font-extrabold text-6xl">
            Meet<br /><span class="text-blue-500">Dr</span>.Jayanta Debbarma
          </h1>
          <p id="intro-text" class="text-xl font-semibold">
          </p>
        </div>
      </section>

      <!-- Journey Section -->
      <section id="journey" class="w-full h-[120vh] mb-[10vh] flex">
        <div class="w-[50%] h-full">
          <div
            class="w-full h-[10%] bg-blue-500 rounded-r-lg flex justify-center items-center"
          >
            <h1 class="font-extrabold text-4xl">Education & Experience</h1>
          </div>

          <div
            class="w-full h-[80%] flex flex-col justify-around items-start pl-16"
          >

            <div
              class="w-[80%] py-3 bg-gray-100 flex items-center gap-8 pl-7 rounded-2xl drop-shadow-md exp-bpx"
            >
              <img
                src="asset/icons/school.png"
                alt=""
                class="w-[3vw] h-[3vw]"
              />
              <div class="flex flex-col minn-w-[50%]">
                <h2 class="text-2xl font-bold">
                  Diploma in Civil Engineering.
                </h2>
                <div class="flex w-full justify-between gap-5">
                  <p class="font-bold text-blue-950">
                    Polytechnic Institute, Narsingarh, Tripura
                  </p>
                  <p>(1988-90)</p>
                </div>
              </div>
            </div>

            <div
              class="w-[80%] py-3 bg-gray-100 flex items-center gap-8 pl-7 rounded-2xl drop-shadow-md exp-bpx"
            >
              <img
                src="asset/icons/briefcase.png"
                alt=""
                class="w-[3vw] h-[3vw]"
              />
              <div class="flex flex-col minn-w-[50%]">
                <h2 class="text-2xl font-bold">Job Joining.</h2>
                <div class="flex w-full justify-between gap-5">
                  <p class="font-bold text-blue-950">
                    P.W.D, W.R, Gov. of Tripura
                  </p>
                  <p>(1991)</p>
                </div>
              </div>
            </div>

            <div
              class="w-[80%] py-3 bg-gray-100 flex items-center gap-8 pl-7 rounded-2xl drop-shadow-md exp-bpx"
            >
              <img
                src="asset/icons/school.png"
                alt=""
                class="w-[3vw] h-[3vw]"
              />
              <div class="flex flex-col minn-w-[50%]">
                <h2 class="text-2xl font-bold">B.Tech in Civil Engineering.</h2>
                <div class="flex w-full gap-5">
                  <p class="font-bold text-blue-950">NERIST</p>
                  <p>(2002)</p>
                </div>
              </div>
            </div>

            <div
              class="w-[80%] py-3 bg-gray-100 flex items-center gap-8 pl-7 rounded-2xl drop-shadow-md exp-bpx"
            >
              <img
                src="asset/icons/champion.png"
                alt=""
                class="w-[3vw] h-[3vw]"
              />
              <div class="flex flex-col minn-w-[50%]">
                <h2 class="text-2xl font-bold">
                  Graduate Aptitude Test in Engineering.
                </h2>
                <div class="flex w-full justify-between gap-5">
                  <!-- <p class="font-bold text-blue-950">Jadavpur University</p> -->
                  <p>(2007)</p>
                </div>
              </div>
            </div>

            <div
              class="w-[80%] py-3 bg-gray-100 flex items-center gap-8 pl-7 rounded-2xl drop-shadow-md exp-bpx"
            >
              <img
                src="asset/icons/mortarboard.png"
                alt=""
                class="w-[3vw] h-[3vw]"
              />
              <div class="flex flex-col minn-w-[50%]">
                <h2 class="text-2xl font-bold">M.E in Civil Engineering.</h2>
                <div class="flex w-full justify-between gap-5">
                  <p class="font-bold text-blue-950">
                    School of Water Resource Engineering, JU.
                  </p>
                  <p>(2008-2010)</p>
                </div>
              </div>
            </div>

            <div
              class="w-[80%] py-3 bg-gray-100 flex items-center gap-8 pl-7 rounded-2xl drop-shadow-md exp-bpx"
            >
              <img
                src="asset/icons/mortarboard.png"
                alt=""
                class="w-[3vw] h-[3vw]"
              />
              <div class="flex flex-col minn-w-[50%]">
                <h2 class="text-2xl font-bold">Ph.D in Ground Water.</h2>
                <div class="flex w-full justify-between gap-5">
                  <p class="font-bold text-blue-950">
                    School of Water Resource Engineering, JU.
                  </p>
                  <p>(2011-2018)</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="w-[50%] h-full relative">
          <!-- Small Cards Ordered from Top to Bottom -->
          <div
            class="absolute bg-white z-1 top-[5%] left-[65%] rotate-[-10deg] w-[var(--card-width-small)] h-[var(--card-height-small)] flex justify-center pt-[2.5%] shadow-blue-500 shadow-2xl border-[1px] border-gray-100 rounded-sm"
            id="box"
          >
            <div
              class="w-[80%] h-[70%] bg-cover bg-center border-[1px] border-gray-100 rounded-sm"
              style="background-image: url('asset/images/minipic1.jpeg')"
            ></div>
          </div>

          <div
            class="absolute bg-white z-1 top-[10%] left-[20%] rotate-[10deg] w-[var(--card-width-small)] h-[var(--card-height-small)] flex justify-center pt-[2.5%] shadow-blue-500 shadow-2xl border-[1px] border-gray-100 rounded-sm"
            id="box"
          >
            <div
              class="w-[80%] h-[70%] bg-cover bg-center border-[1px] border-gray-100 rounded-sm"
              style="background-image: url('asset/images/minipic2.jpeg')"
            ></div>
          </div>

          <div
            class="absolute bg-white z-1 top-[40%] left-[70%] rotate-[15deg] w-[var(--card-width-small)] h-[var(--card-height-small)] flex justify-center pt-[2.5%] shadow-blue-500 shadow-2xl border-[1px] border-gray-100 rounded-sm"
            id="box"
          >
            <div
              class="w-[80%] h-[70%] bg-cover bg-center border-[1px] border-gray-100 rounded-sm"
              style="background-image: url('asset/images/minipic3.jpeg')"
            ></div>
          </div>

          <div
            class="absolute bg-white z-1 top-[40%] left-[10%] rotate-[-15deg] w-[var(--card-width-small)] h-[var(--card-height-small)] flex justify-center pt-[2.5%] shadow-blue-500 shadow-2xl border-[1px] border-gray-100 rounded-sm"
            id="box"
          >
            <div
              class="w-[80%] h-[70%] bg-cover bg-center border-[1px] border-gray-100 rounded-sm"
              style="background-image: url('asset/images/minipic4.jpg')"
            ></div>
          </div>

          <div
            class="absolute bg-white z-1 top-[70%] left-[20%] rotate-[5deg] w-[var(--card-width-small)] h-[var(--card-height-small)] flex justify-center pt-[2.5%] shadow-blue-500 shadow-2xl border-[1px] border-gray-100 rounded-sm"
            id="box"
          >
            <div
              class="w-[80%] h-[70%] bg-cover bg-center border-[1px] border-gray-100 rounded-sm"
              style="background-image: url('asset/images/minipic5.jpeg')"
            ></div>
          </div>

          <div
            class="absolute bg-white z-1 top-[72%] left-[60%] rotate-[-5deg] w-[var(--card-width-small)] h-[var(--card-height-small)] flex justify-center pt-[2.5%] shadow-blue-500 shadow-2xl border-[1px] border-gray-100 rounded-sm"
            id="box"
          >
            <div
              class="w-[80%] h-[70%] bg-cover bg-center border-[1px] border-gray-100 rounded-sm"
              style="background-image: url('asset/images/minipic6.jpeg')"
            ></div>
          </div>

          <!-- Large Card (Placed Last in Code) -->
          <div
            class="absolute top-[25%] left-[30%] w-[var(--card-width-large)] h-[var(--card-height-large)] z-50 flex justify-center pt-[4.5%] shadow-blue-500 shadow-2xl border-[1px] border-gray-100 rounded-sm bg-white"
          >
            <div
              class="w-[80%] h-[70%] bg-cover bg-center border-[1px] border-gray-100 rounded-sm"
              style="background-image: url('asset/phd.jpg')"
            ></div>
          </div>
        </div>
      </section>

      <!-- <section class="h-[30vh] w-full"></section> -->

      <!-- Journals Section -->
      <section id="journals" class="w-full h-[120vh] mb-[20vh] flex">
        <div class="relative w-[50%] h-[80%] overflow-hidden">
          <!-- Blurred Circle -->
          <div
            class="absolute bg-blue-500 w-[80vw] md:w-[50vw] h-[50vw] md:h-[30vw] top-[20%] left-[-20%] md:left-[-10%] rounded-full blur-xl z-0"
            id="blured-bg-journals"
          ></div>

          <!-- Image -->
          <img
            src="asset/images/degree.png"
            alt="Degree Image"
            class="absolute w-full h-full object-contain md:object-cover z-10"
          />
        </div>

        <div
          class="w-[50%] h-full flex flex-col items-center justify-center gap-7"
        >
          <h1 class="bg-blue-500 font-extrabold text-5xl px-6 py-7 rounded-2xl">
            Journals & Papers
          </h1>
          <div class="flex flex-col h-[90%] justify-around items-center w-full">
            <a
              class="flex items-center space-x-3 max-w-[90%] px-5 py-2 rounded-2xl bg-gray-100 drop-shadow-md gap-2 w-full h-[8%] cursor-pointer jp-card"
              href="https://scholar.google.co.in/citations?view_op=view_citation&hl=en&user=I4M4AewAAAAJ&citation_for_view=I4M4AewAAAAJ:d1gkVwhDpl0C"
              target="_blank"
            >
              <img
                src="asset/icons/newspaper-folded.png"
                alt="Paper Icon"
                class="w-6 h-6"
              />
              <p class="font-semibold">
                Assessment of dynamic groundwater potential of Agartala Municipality Area.
              </p>
            </a>
            <a
              class="flex items-center space-x-3 max-w-[90%] px-3 py-2 rounded-2xl bg-gray-100 drop-shadow-md gap-2 w-full h-[8%] cursor-pointer jp-card"
              href="https://scholar.google.co.in/citations?view_op=view_citation&hl=en&user=I4M4AewAAAAJ&citation_for_view=I4M4AewAAAAJ:W7OEmFMy1HYC"
              target="_blank"
            >
              <img
                src="asset/icons/newspaper-folded.png"
                alt="Paper Icon"
                class="w-6 h-6"
              />
              <p class="font-semibold">
                Development of an environmentally sustainable approach for safe disposal of arsenic-rich sludge.
              </p>
            </a>
            <a
              class="flex items-center space-x-3 max-w-[90%] px-3 py-2 rounded-2xl bg-gray-100 drop-shadow-md gap-2 w-full h-[8%] cursor-pointer jp-card"
              href="https://scholar.google.co.in/citations?view_op=view_citation&hl=en&user=I4M4AewAAAAJ&citation_for_view=I4M4AewAAAAJ:9yKSN-GCB0IC"
              target="_blank"
            >
              <img
                src="asset/icons/newspaper-folded.png"
                alt="Paper Icon"
                class="w-6 h-6"
              />
              <p class="font-semibold">
                First investigation of the climate change impact on the crop productivity of the Piyali River region.
              </p>
            </a>
            <a
              class="flex items-center space-x-3 max-w-[90%] px-3 py-2 rounded-2xl bg-gray-100 drop-shadow-md gap-2 w-full h-[8%] cursor-pointer jp-card"
              href="https://scholar.google.co.in/citations?view_op=view_citation&hl=en&user=I4M4AewAAAAJ&citation_for_view=I4M4AewAAAAJ:u5HHmVD_uO8C"
              target="_blank"
            >
              <img
                src="asset/icons/newspaper-folded.png"
                alt="Paper Icon"
                class="w-6 h-6"
              />
              <p class="font-semibold">
                Estimating groundwater volumetric mass balance with hydraulic head using groundwater modeling system in Tripura, India.
              </p>
            </a>
            <a
              class="flex items-center space-x-3 max-w-[90%] px-3 py-2 rounded-2xl bg-gray-100 drop-shadow-md gap-2 w-full h-[8%] cursor-pointer jp-card"
              href="https://scholar.google.co.in/citations?view_op=view_citation&hl=en&user=I4M4AewAAAAJ&citation_for_view=I4M4AewAAAAJ:2osOgNQ5qMEC"
              target="_blank"
            >
              <img
                src="asset/icons/newspaper-folded.png"
                alt="Paper Icon"
                class="w-6 h-6"
              />
              <p class="font-semibold">
                Developing a correlation for estimation of aquifer layer using resistivity survey with lithological logs in critical terrain condition.
              </p>
            </a>
            <a
              class="flex items-center space-x-3 max-w-[90%] px-3 py-2 rounded-2xl bg-gray-100 drop-shadow-md gap-2 w-full h-[8%] cursor-pointer jp-card"
              href="https://scholar.google.co.in/citations?view_op=view_citation&hl=en&user=I4M4AewAAAAJ&citation_for_view=I4M4AewAAAAJ:UeHWp8X0CEIC"
              target="_blank"
            >
              <img
                src="asset/icons/newspaper-folded.png"
                alt="Paper Icon"
                class="w-6 h-6"
              />
              <p class="font-semibold">
                Critical study of sub-surface aquifer layer for groundwater availability based on electrical resistivity survey: A part of Dhalai Tripura, India.
              </p>
            </a>
            <a
              class="flex items-center space-x-3 max-w-[90%] px-3 py-2 rounded-2xl bg-gray-100 drop-shadow-md gap-2 w-full h-[8%] cursor-pointer jp-card"
              href="https://scholar.google.co.in/citations?view_op=view_citation&hl=en&user=I4M4AewAAAAJ&citation_for_view=I4M4AewAAAAJ:u-x6o8ySG0sC"
              target="_blank"
            >
              <img
                src="asset/icons/newspaper-folded.png"
                alt="Paper Icon"
                class="w-6 h-6"
              />
              <p class="font-semibold">
                Study of identification of effective sand bed in aquifer zones using resistivity survey in Tripura: Case studies.
              </p>
            </a>
            <a
              class="flex items-center space-x-3 max-w-[90%] px-3 py-2 rounded-2xl bg-gray-100 drop-shadow-md gap-2 w-full h-[8%] cursor-pointer jp-card"
              href="https://scholar.google.co.in/citations?view_op=view_citation&hl=en&user=I4M4AewAAAAJ&citation_for_view=I4M4AewAAAAJ:YsMSGLbcyi4C"
              target="_blank"
            >
              <img
                src="asset/icons/newspaper-folded.png"
                alt="Paper Icon"
                class="w-6 h-6"
              />
              <p class="font-semibold">
                Critical assessment of groundwater overflow zone for sustainable management in North East India.
              </p>
            </a>
            <a
              class="flex items-center space-x-3 max-w-[90%] px-3 py-2 rounded-2xl bg-gray-100 drop-shadow-md gap-2 w-full h-[8%] cursor-pointer jp-card"
              href="https://scholar.google.co.in/citations?view_op=view_citation&hl=en&user=I4M4AewAAAAJ&citation_for_view=I4M4AewAAAAJ:qjMakFHDy7sC"
              target="_blank"
            >
              <img
                src="asset/icons/newspaper-folded.png"
                alt="Paper Icon"
                class="w-6 h-6"
              />
              <p class="font-semibold">
                Estimation of aquifer thickness of groundwater using resistivity survey in Tripura.
              </p>
            </a>
          </div>
        </div>
      </section>

      <!-- <section class="h-[20vh] w-full"></section> -->

      <!-- Thesis Section -->
      <section id="thesis" class="w-full h-[90vh] mb-[10vh] flex flex-col">
        <div class="w-full h-[20%] flex justify-center items-center">
          <div
            class="py-6 px-7 rounded-2xl flex justify-center items-center bg-blue-500"
            id="blue-box-thesis"
          >
            <h1 class="text-5xl font-bold z-[10]">Thesis & Chapters</h1>
          </div>
        </div>
        <div class="w-full h-[80%] flex items-center justify-around">
          <a
            class="flex flex-col justify-around text-center items-center space-x-3 px-3 py-2 rounded-2xl bg-gray-100 drop-shadow-md gap-2 cursor-pointer jp-card max-w-[45%] h-[90%] relative"
          >
            <div class="flex flex-col justify-between items-center">
              <img
                src="asset/icons/newspaper-folded.png"
                alt="Paper Icon"
                class="w-10 h-10"
              />
              <p class="font-bold">
                STUDY ON GROUNDWATER ASSESSMENT, DEVELOPMENT AND MANAGEMENT IN
                THE STATE OF TRIPURA.
              </p>
            </div>
            <p>
              Thesis Submitted By <br /><span class="font-bold"
                >Jayanta Debbarma</span
              >
              <br />
              Doctor of philosophy (Engineering)
            </p>
            <p class="font-bold text-blue-950">
              SCHOOL OF WATER RESOURCES ENGINEERING JADAVPUR UNIVERSITY, KOLKATA
              - 700032, INDIA
            </p>
          </a>

          <a
            class="flex flex-col justify-around text-center items-center space-x-3 px-3 py-2 rounded-2xl bg-gray-100 drop-shadow-md gap-2 cursor-pointer jp-card max-w-[45%] h-[90%] relative"
          >
            <div class="flex flex-col justify-between items-center">
              <img
                src="asset/icons/newspaper-folded.png"
                alt="Paper Icon"
                class="w-10 h-10"
              />
              <p class="font-bold">
                Assessment of Dynamic Groundwater Potential of Agartala
                Municipality Area.
              </p>
            </div>
            <p>
              Thesis Submitted By <br /><span class="font-bold"
                >Jayanta Debbarma</span
              >
            </p>
            <p>
              Under the guidance of <br />
              <span class="font-bold">Prof.(Dr) Asis Mazumdar</span> <br />
              Director<br />
              <span class="font-bold">Dr. Pankaj Kumar Roy</span> <br />
              Lecturer
            </p>
            <p class="font-bold text-blue-950">
              SCHOOL OF WATER RESOURCES ENGINEERING JADAVPUR UNIVERSITY, KOLKATA
              - 700032, INDIA
            </p>
          </a>
        </div>
      </section>

      <!-- <section class="h-[20vh] w-full"></section> -->

      <!-- Advisor's Section -->
      <section id="advisor" class="w-full h-[90vh] mb-[10vh] flex flex-col">
        <div class="w-full h-[20%] flex justify-center items-center">
          <div
            class="py-6 px-7 rounded-2xl flex justify-center items-center bg-blue-500"
            id="blue-box-advisor"
          >
            <h1 class="text-5xl font-bold z-[10]">Key Advisors</h1>
          </div>
        </div>
        <div class="w-full h-[80%] flex items-center justify-around">
          <div class="w-[30%] h-[90%]  rounded-2xl flex flex-col justify-around items-center bg-gray-200  drop-shadow-2xl">
            <img
              data-src="asset/images/pankajKrRoy.png"
              src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
              alt="Pankaj Kr Roy"
              class="bg-white w-full max-w-[12vw] h-auto rounded-2xl object-cover lazy"
            />
            <div class="w-[90%]  text-center flex flex-col ">
              <h1 class="font-bold text-2xl">Prof (Dr.) Pankaj Kumar Roy</h1>
              <p>Former Dean FISLM & Director of School of Water Resources Engineering, Jadavpur University.</p>
              <h3 class="font-bold text-xl">Chief Advisor</h3>
            </div>
          </div>
          <div class="w-[30%] h-[90%]  rounded-2xl flex flex-col justify-around items-center bg-gray-200 drop-shadow-2xl">
            <img
              data-src="asset/images/manish.png"
              src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
              alt="Dr. Manish Pal"
              class="bg-white w-full max-w-[12vw] h-auto rounded-2xl object-cover lazy"
            />
            <div class="w-[90%]  text-center flex flex-col ">
              <h1 class="font-bold text-2xl">Dr.Manish Pal</h1>
              <p>Professor of Civil Engineering Department <br> NIT Agartala</p>
              <h3 class="font-bold text-xl">Advisor</h3>
            </div>
          </div>
      </section>

      <!-- <section class="h-[20vh] w-full"></section> -->

      <!-- Interest Section -->
      <section id="interest" class="w-full h-[90vh] mb-[10vh] flex flex-col">
        <div class="w-full h-[20%] flex justify-center items-center">
          <div
            class="py-6 px-7 rounded-2xl flex justify-center items-center bg-blue-500"
            id="blue-box-interest"
          >
            <h1 class="text-5xl font-bold z-[10]">Field of Interest</h1>
          </div>
        </div>
        <div class="w-full h-[40%] flex border-y-1 border-gray-300">
          <div class="h-full w-[20%] flex justify-center items-center">
            <h1 class="font-bold text-4xl">PDFs <br> & <br> Documents</h1>
          </div>
          <div class="h-full w-[70%] flex justify-around items-center">
            <div class="w-[18%] h-[90%] bg-black rounded-2xl">
              <img
              class="w-full h-full object-cover rounded-2xl drop-shadow-md lazy"
              data-src="asset/images/pdf1.png"
              src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
              alt="Dr. Jayanta Debbarma"
            />
            </div>
            <div class="w-[18%] h-[90%] bg-black rounded-2xl">
              <img
              class="w-full h-full object-cover rounded-2xl drop-shadow-md lazy"
              data-src="asset/images/pdf2.png"
              src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
              alt="Dr. Jayanta Debbarma"
            />
            </div>
            <div class="w-[18%] h-[90%] bg-black rounded-2xl">
              <img
              class="w-full h-full object-cover rounded-2xl drop-shadow-md lazy"
              data-src="asset/images/pdf3.png"
              src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
              alt="Dr. Jayanta Debbarma"
            />
            </div>
            <div class="w-[18%] h-[90%] bg-black rounded-2xl">
              <img
              class="w-full h-full object-cover rounded-2xl drop-shadow-md"
              src="asset/images/pdf4.png"
              alt="Dr. Jayanta Debbarma"
            />
            </div>
          </div>
          <div class="h-full w-[10%]  flex items-center">
            <button class="px-4 py-3 bg-blue-500 font-bold rounded-2xl cursor-pointer hover:bg-blue-600 focus:text-white">View More</button>
          </div>
        </div>
        <div class="w-full h-[40%] border-y-1 border-gray-300 flex ">
          <div class="h-full w-[20%] flex justify-center items-center">
            <h1 class="font-bold text-4xl">Images <br> & <br> Videos</h1>
          </div>
          <div class="h-full w-[70%] flex justify-around items-center">
            <div class="w-[18%] h-[90%] bg-black rounded-2xl">
              <img
              class="w-full h-full object-cover rounded-2xl drop-shadow-md"
              src="asset/images/field1.jpeg"
              alt="Dr. Jayanta Debbarma"
            />
            </div>
            <div class="w-[18%] h-[90%] bg-black rounded-2xl">
              <img
              class="w-full h-full object-cover rounded-2xl drop-shadow-md"
              src="asset/images/field2.jpeg"
              alt="Dr. Jayanta Debbarma"
            />
            </div>
            <div class="w-[18%] h-[90%] bg-black rounded-2xl">
              <img
              class="w-full h-full object-cover rounded-2xl drop-shadow-md"
              src="asset/images/field3.jpeg"
              alt="Dr. Jayanta Debbarma"
            />
            </div>
            <div class="w-[18%] h-[90%] bg-black rounded-2xl">
              <img
              class="w-full h-full object-cover rounded-2xl drop-shadow-md"
              src="asset/images/field4.jpeg"
              alt="Dr. Jayanta Debbarma"
            />
            </div>
          </div>
          <div class="h-full w-[10%]  flex items-center">
            <button class="px-4 py-3 bg-blue-500 font-bold rounded-2xl cursor-pointer hover:bg-blue-600 focus:text-white">View More</button>
          </div>
        </div>

      </section>

      <!-- <section class="h-[20vh] w-full"></section> -->

      <!-- Contact Section -->
      <section
        id="contact"
        class="w-full h-[120vh] pb-[20vh] bg-gradient-to-t from-blue-500 to-white flex justify-center items-center"
      >
        <div
          class="w-[50%]  rounded-3xl shadow-2xl bg-white p-8 flex flex-col justify-center py-10"
        >
          <h2 class="text-2xl font-bold text-center mb-6 text-gray-700">
            Send Message to Dr.Jayanta Debbarma
          </h2>

          <form class="space-y-4" method="POST" action="src/send_mail.php">
            <div>
              <label class="block text-gray-600 font-medium mb-1">Name</label>
              <input
                type="text"
                name="name"
                class="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-400 outline-none"
                placeholder="Enter your name"
                required
              />
            </div>

            <div>
              <label class="block text-gray-600 font-medium mb-1">Email</label>
              <input
                type="email"
                name="email"
                class="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-400 outline-none"
                placeholder="Enter your email"
                required
              />
            </div>

            <div>
              <label class="block text-gray-600 font-medium mb-1">Phone</label>
              <input
                type="tel"
                name="phone"
                class="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-400 outline-none"
                placeholder="Enter your phone number"
              />
            </div>

            <div>
              <label class="block text-gray-600 font-medium mb-1"
                >Message</label
              >
              <textarea
                name="message"
                class="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-400 outline-none resize-none h-24"
                placeholder="Enter your message"
                required
              ></textarea>
            </div>

            <button
              type="submit"
              name="send"
              class="w-full bg-blue-500 text-white py-3 rounded-lg hover:bg-blue-600 transition duration-300 cursor-pointer"
            >
              Send Message
            </button>
          </form>
        </div>
      </section>

      <section class="w-full h-[90vh] bg-blue-500 flex pb-3 justify-around">
        <div class="w-[60%] h-full pl-7 flex flex-col justify-between">
          <h1 class="text-7xl font-extrabold text-white">
            Dr.Jayanta Debbarma.
          </h1>
          <h1 class="text-xl font-bold text-white">
            © 2025 iLogitron Technologies (P) Ltd. All Right Reserved.
          </h1>
        </div>
        <div class="w-[40%] h-full"></div>
      </section>
    </main>

    <script>
      document.addEventListener("DOMContentLoaded", () => {
        const sections = document.querySelectorAll("section");
        const navLinks = document.querySelectorAll("#nav-buttons a");

        const observerOptions = {
          root: null, // viewport
          rootMargin: "0px",
          threshold: 0.5, // Section should be at least 50% visible
        };

        const observerCallback = (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              // Remove active class from all links
              navLinks.forEach((link) =>
                link.classList.remove("text-blue-500", "font-bold")
              );

              // Find the active section's corresponding link
              const activeLink = document.querySelector(
                `#nav-buttons a[href="#${entry.target.id}"]`
              );
              if (activeLink) {
                activeLink.classList.add("text-blue-500", "font-bold");
              }
            }
          });
        };

        const observer = new IntersectionObserver(
          observerCallback,
          observerOptions
        );

        sections.forEach((section) => observer.observe(section));

        // Check for status in URL and show alert
        const urlParams = new URLSearchParams(window.location.search);
        const status = urlParams.get("status");
        if (status === "success") {
          alert("Message sent successfully!");
          window.history.replaceState(
            {},
            document.title,
            window.location.pathname
          ); // Clear query param
        } else if (status === "error") {
          alert("Failed to send message. Please try again.");
          window.history.replaceState(
            {},
            document.title,
            window.location.pathname
          ); // Clear query param
        }
      });
    </script>

     <p
      class="bg-blue-500 rounded-l-2xl"
      style="
        position: fixed;
        right: 0px;
        bottom: 0px;
        color: white;
        padding: 8px 20px;
        border: left 8px;
        z-index: 10000;
      "
    >
      Visitors: <span id="visitorCount">Loading...</span>
    </p>

    <!-- Defer non-critical JavaScript -->
    <script>
      // Visitor counter - load after page content
      window.addEventListener('load', function() {
        fetch("visitor.php?page=home")
          .then((response) => {
            if (!response.ok) {
              throw new Error('Network response was not ok');
            }
            return response.text();
          })
          .then((data) => {
            // Make sure we got a valid number
            const count = parseInt(data.trim());
            if (!isNaN(count) && count >= 0) {
              document.getElementById("visitorCount").textContent = count;
            } else {
              // Fallback to 0 if invalid response
              document.getElementById("visitorCount").textContent = "0";
            }
          })
          .catch((error) => {
            console.error("Error fetching visitor count:", error);
            // Set default value on error
            document.getElementById("visitorCount").textContent = "0";
          });
      });
    </script>

    <!-- GSAP Core with preconnect for faster loading -->
    <link rel="preconnect" href="https://cdn.jsdelivr.net" crossorigin>
    <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.5/dist/gsap.min.js" defer></script>
    <!-- ScrollTrigger Plugin -->
    <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.5/dist/ScrollTrigger.min.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.7/dist/TextPlugin.min.js" defer></script>

    <!-- Main script with defer attribute -->
    <script src="src/script.js" defer></script>

    <!-- WebP support script -->
    <script src="src/webp_support.js" defer></script>

    <!-- Lazy loading script -->
    <script src="src/lazy-load.js" defer></script>
    
    <!-- Add the new responsive script here -->
    <script>
      // Add this to handle responsive scaling
      function adjustForScreenSize() {
        const width = window.innerWidth;
        const height = window.innerHeight;
        const ratio = width / height;
        
        // Adjust layout based on screen ratio
        if (ratio < 1.5) { // Narrower screens
          document.documentElement.classList.add('narrow-screen');
        } else {
          document.documentElement.classList.remove('narrow-screen');
        }
        
        // You can add more specific adjustments here
      }
      
      // Run on load and resize
      window.addEventListener('load', adjustForScreenSize);
      window.addEventListener('resize', adjustForScreenSize);
    </script>
  </body>
</html>
