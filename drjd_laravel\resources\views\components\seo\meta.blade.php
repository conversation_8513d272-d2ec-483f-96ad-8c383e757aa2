@php
    $seoData = $seoSettings ?? null;
@endphp

<!-- Basic Meta Tags -->
<title>{{ $seoData->meta_title ?? config('app.name') }}</title>
<meta name="description" content="{{ $seoData->meta_description ?? 'Dr. Jaya<PERSON> - Academic Portfolio' }}">
@if($seoData && $seoData->meta_keywords)
    <meta name="keywords" content="{{ $seoData->meta_keywords }}">
@endif

<!-- Robots Meta -->
<meta name="robots" content="{{ $seoData && !$seoData->index_page ? 'noindex' : 'index' }},{{ $seoData && !$seoData->follow_links ? 'nofollow' : 'follow' }}">

<!-- Canonical URL -->
@if($seoData && $seoData->canonical_url)
    <link rel="canonical" href="{{ $seoData->canonical_url }}">
@else
    <link rel="canonical" href="{{ url()->current() }}">
@endif

<!-- Open Graph Meta Tags -->
<meta property="og:title" content="{{ $seoData->og_title ?? $seoData->meta_title ?? config('app.name') }}">
<meta property="og:description" content="{{ $seoData->og_description ?? $seoData->meta_description ?? 'Dr. Jayanta Debbarma - Academic Portfolio' }}">
<meta property="og:type" content="website">
<meta property="og:url" content="{{ url()->current() }}">
@if($seoData && $seoData->og_image)
    <meta property="og:image" content="{{ asset($seoData->og_image) }}">
@endif

<!-- Twitter Card Meta Tags -->
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="{{ $seoData->og_title ?? $seoData->meta_title ?? config('app.name') }}">
<meta name="twitter:description" content="{{ $seoData->og_description ?? $seoData->meta_description ?? 'Dr. Jayanta Debbarma - Academic Portfolio' }}">
@if($seoData && $seoData->og_image)
    <meta name="twitter:image" content="{{ asset($seoData->og_image) }}">
@endif

<!-- Structured Data -->
@if($seoData && $seoData->structured_data)
    <script type="application/ld+json">
        {!! json_encode($seoData->structured_data, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) !!}
    </script>
@endif
