<nav class="bg-white shadow-lg">
    <div class="container mx-auto px-4">
        <div class="flex justify-between items-center py-4">
            <!-- Logo/Brand -->
            <div class="flex items-center">
                <a href="{{ route('home') }}" class="text-xl font-bold text-gray-800">
                    {{ $structure->honorific ?? 'Dr.' }} {{ $structure->name ?? 'Jayanta Debbarma' }}
                </a>
            </div>

            <!-- Desktop Navigation -->
            <div class="hidden md:flex space-x-8">
                <a href="{{ route('home') }}" class="text-gray-600 hover:text-gray-900 transition duration-300">
                    {{ $structure->page1 ?? 'Home' }}
                </a>
                <a href="{{ route('about') }}" class="text-gray-600 hover:text-gray-900 transition duration-300">
                    {{ $structure->page2 ?? 'About' }}
                </a>
                <a href="{{ route('journey') }}" class="text-gray-600 hover:text-gray-900 transition duration-300">
                    {{ $structure->page3 ?? 'Journey' }}
                </a>
                <a href="{{ route('journals') }}" class="text-gray-600 hover:text-gray-900 transition duration-300">
                    {{ $structure->page4 ?? 'Journals' }}
                </a>
                <a href="{{ route('thesis') }}" class="text-gray-600 hover:text-gray-900 transition duration-300">
                    {{ $structure->page5 ?? 'Thesis' }}
                </a>
                <a href="{{ route('advisors') }}" class="text-gray-600 hover:text-gray-900 transition duration-300">
                    {{ $structure->page6 ?? 'Advisors' }}
                </a>
                <a href="{{ route('field-interest') }}" class="text-gray-600 hover:text-gray-900 transition duration-300">
                    {{ $structure->page7 ?? 'Field of Interest' }}
                </a>
                <a href="{{ route('contact') }}" class="text-gray-600 hover:text-gray-900 transition duration-300">
                    {{ $structure->page8 ?? 'Contact' }}
                </a>
            </div>

            <!-- Mobile menu button -->
            <div class="md:hidden">
                <button id="mobile-menu-button" class="text-gray-600 hover:text-gray-900 focus:outline-none">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="hidden md:hidden pb-4">
            <a href="{{ route('home') }}" class="block py-2 text-gray-600 hover:text-gray-900">{{ $structure->page1 ?? 'Home' }}</a>
            <a href="{{ route('about') }}" class="block py-2 text-gray-600 hover:text-gray-900">{{ $structure->page2 ?? 'About' }}</a>
            <a href="{{ route('journey') }}" class="block py-2 text-gray-600 hover:text-gray-900">{{ $structure->page3 ?? 'Journey' }}</a>
            <a href="{{ route('journals') }}" class="block py-2 text-gray-600 hover:text-gray-900">{{ $structure->page4 ?? 'Journals' }}</a>
            <a href="{{ route('thesis') }}" class="block py-2 text-gray-600 hover:text-gray-900">{{ $structure->page5 ?? 'Thesis' }}</a>
            <a href="{{ route('advisors') }}" class="block py-2 text-gray-600 hover:text-gray-900">{{ $structure->page6 ?? 'Advisors' }}</a>
            <a href="{{ route('field-interest') }}" class="block py-2 text-gray-600 hover:text-gray-900">{{ $structure->page7 ?? 'Field of Interest' }}</a>
            <a href="{{ route('contact') }}" class="block py-2 text-gray-600 hover:text-gray-900">{{ $structure->page8 ?? 'Contact' }}</a>
        </div>
    </div>
</nav>

@push('scripts')
<script>
    document.getElementById('mobile-menu-button').addEventListener('click', function() {
        const mobileMenu = document.getElementById('mobile-menu');
        mobileMenu.classList.toggle('hidden');
    });
</script>
@endpush
