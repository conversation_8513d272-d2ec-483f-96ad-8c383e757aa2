<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\FieldInterestPdf;

class FieldInterestPdfController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $pdfs = FieldInterestPdf::orderBy('display_date', 'desc')->get();
        return view('admin.field-interest-pdf.index', compact('pdfs'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.field-interest-pdf.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'pdf_file' => 'required|string|max:255',
            'thumbnail' => 'nullable|string|max:255',
            'display_date' => 'required|date',
            'show_on_home' => 'boolean',
        ]);

        $data = $request->all();
        $data['upload_date'] = now();
        $data['show_on_home'] = $request->has('show_on_home');

        FieldInterestPdf::create($data);

        return redirect()->route('admin.field-interest-pdf.index')
            ->with('success', 'PDF document created successfully');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(FieldInterestPdf $fieldInterestPdf)
    {
        return view('admin.field-interest-pdf.edit', compact('fieldInterestPdf'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, FieldInterestPdf $fieldInterestPdf)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'pdf_file' => 'required|string|max:255',
            'thumbnail' => 'nullable|string|max:255',
            'display_date' => 'required|date',
            'show_on_home' => 'boolean',
        ]);

        $data = $request->all();
        $data['show_on_home'] = $request->has('show_on_home');

        $fieldInterestPdf->update($data);

        return redirect()->route('admin.field-interest-pdf.index')
            ->with('success', 'PDF document updated successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(FieldInterestPdf $fieldInterestPdf)
    {
        $fieldInterestPdf->delete();

        return redirect()->route('admin.field-interest-pdf.index')
            ->with('success', 'PDF document deleted successfully');
    }
}
