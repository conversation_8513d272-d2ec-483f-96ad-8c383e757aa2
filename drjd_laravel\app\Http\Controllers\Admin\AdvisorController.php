<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Advisor;

class AdvisorController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $advisors = Advisor::orderBy('id', 'desc')->get();
        return view('admin.advisor.index', compact('advisors'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.advisor.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'designation' => 'required|string|max:255',
            'rank' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'image' => 'nullable|string|max:255',
        ]);

        Advisor::create($request->all());

        return redirect()->route('admin.advisor.index')
            ->with('success', 'Advisor created successfully');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Advisor $advisor)
    {
        return view('admin.advisor.edit', compact('advisor'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Advisor $advisor)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'designation' => 'required|string|max:255',
            'rank' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'image' => 'nullable|string|max:255',
        ]);

        $advisor->update($request->all());

        return redirect()->route('admin.advisor.index')
            ->with('success', 'Advisor updated successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Advisor $advisor)
    {
        $advisor->delete();

        return redirect()->route('admin.advisor.index')
            ->with('success', 'Advisor deleted successfully');
    }
}
