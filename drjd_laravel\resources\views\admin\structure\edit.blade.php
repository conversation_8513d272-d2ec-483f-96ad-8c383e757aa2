<x-layout.admin title="Edit Site Structure">
    <div class="mb-8">
        <div class="flex items-center space-x-4">
            <a href="{{ route('admin.structure.index') }}" class="text-gray-600 hover:text-gray-800">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Edit Site Structure</h1>
                <p class="text-gray-600">Configure your site's basic information and navigation</p>
            </div>
        </div>
    </div>

    <form method="POST" action="{{ route('admin.structure.update') }}" class="space-y-8">
        @csrf
        @method('PUT')
        
        <!-- Basic Information -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-6">Basic Information</h2>
            
            <div class="grid gap-6">
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <label for="honorific" class="block text-sm font-medium text-gray-700 mb-2">Honorific *</label>
                        <input type="text" id="honorific" name="honorific" required
                               value="{{ old('honorific', $structure->honorific ?? 'Dr.') }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                               placeholder="Dr., Prof., etc.">
                        @error('honorific')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                        <input type="text" id="name" name="name" required
                               value="{{ old('name', $structure->name ?? '') }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                               placeholder="Your full name">
                        @error('name')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
                
                <div>
                    <label for="short_bio" class="block text-sm font-medium text-gray-700 mb-2">Short Bio *</label>
                    <input type="text" id="short_bio" name="short_bio" required
                           value="{{ old('short_bio', $structure->short_bio ?? '') }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                           placeholder="Brief professional description">
                    @error('short_bio')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="moto" class="block text-sm font-medium text-gray-700 mb-2">Motto *</label>
                    <input type="text" id="moto" name="moto" required
                           value="{{ old('moto', $structure->moto ?? '') }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                           placeholder="Your professional motto or tagline">
                    @error('moto')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Navigation Pages -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-6">Navigation Pages</h2>
            
            <div class="grid md:grid-cols-4 gap-6">
                <div>
                    <label for="page1" class="block text-sm font-medium text-gray-700 mb-2">Page 1 *</label>
                    <input type="text" id="page1" name="page1" required
                           value="{{ old('page1', $structure->page1 ?? 'Home') }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                    @error('page1')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="page2" class="block text-sm font-medium text-gray-700 mb-2">Page 2 *</label>
                    <input type="text" id="page2" name="page2" required
                           value="{{ old('page2', $structure->page2 ?? 'About') }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                    @error('page2')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="page3" class="block text-sm font-medium text-gray-700 mb-2">Page 3 *</label>
                    <input type="text" id="page3" name="page3" required
                           value="{{ old('page3', $structure->page3 ?? 'Journey') }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                    @error('page3')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="page4" class="block text-sm font-medium text-gray-700 mb-2">Page 4 *</label>
                    <input type="text" id="page4" name="page4" required
                           value="{{ old('page4', $structure->page4 ?? 'Journals') }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                    @error('page4')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="page5" class="block text-sm font-medium text-gray-700 mb-2">Page 5 *</label>
                    <input type="text" id="page5" name="page5" required
                           value="{{ old('page5', $structure->page5 ?? 'Thesis') }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                    @error('page5')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="page6" class="block text-sm font-medium text-gray-700 mb-2">Page 6 *</label>
                    <input type="text" id="page6" name="page6" required
                           value="{{ old('page6', $structure->page6 ?? 'Advisors') }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                    @error('page6')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="page7" class="block text-sm font-medium text-gray-700 mb-2">Page 7 *</label>
                    <input type="text" id="page7" name="page7" required
                           value="{{ old('page7', $structure->page7 ?? 'Field of Interest') }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                    @error('page7')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="page8" class="block text-sm font-medium text-gray-700 mb-2">Page 8 *</label>
                    <input type="text" id="page8" name="page8" required
                           value="{{ old('page8', $structure->page8 ?? 'Contact') }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                    @error('page8')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Page Titles -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-6">Page Titles</h2>
            
            <div class="grid md:grid-cols-2 gap-6">
                <div>
                    <label for="page3_title" class="block text-sm font-medium text-gray-700 mb-2">Journey Page Title *</label>
                    <input type="text" id="page3_title" name="page3_title" required
                           value="{{ old('page3_title', $structure->page3_title ?? 'Education & Experience') }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                    @error('page3_title')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="page4_title" class="block text-sm font-medium text-gray-700 mb-2">Journals Page Title *</label>
                    <input type="text" id="page4_title" name="page4_title" required
                           value="{{ old('page4_title', $structure->page4_title ?? 'Journals & Papers') }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                    @error('page4_title')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="page5_title" class="block text-sm font-medium text-gray-700 mb-2">Thesis Page Title *</label>
                    <input type="text" id="page5_title" name="page5_title" required
                           value="{{ old('page5_title', $structure->page5_title ?? 'Thesis & Chapters') }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                    @error('page5_title')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="page6_title" class="block text-sm font-medium text-gray-700 mb-2">Advisors Page Title *</label>
                    <input type="text" id="page6_title" name="page6_title" required
                           value="{{ old('page6_title', $structure->page6_title ?? 'Key Advisors') }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                    @error('page6_title')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="page7_title" class="block text-sm font-medium text-gray-700 mb-2">Field of Interest Page Title *</label>
                    <input type="text" id="page7_title" name="page7_title" required
                           value="{{ old('page7_title', $structure->page7_title ?? 'Field of Interest') }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                    @error('page7_title')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- About Section -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-6">About Section</h2>
            
            <div>
                <label for="about" class="block text-sm font-medium text-gray-700 mb-2">About Content *</label>
                <textarea id="about" name="about" rows="10" required
                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                          placeholder="Write your detailed about section...">{{ old('about', $structure->about ?? '') }}</textarea>
                @error('about')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex items-center justify-between">
            <a href="{{ route('admin.structure.index') }}" 
               class="bg-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-400 transition duration-300">
                Cancel
            </a>
            
            <button type="submit" 
                    class="bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition duration-300">
                <i class="fas fa-save mr-2"></i>
                Save Structure
            </button>
        </div>
    </form>
</x-layout.admin>
