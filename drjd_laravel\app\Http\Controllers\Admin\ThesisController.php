<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ThesisChapter;

class ThesisController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $theses = ThesisChapter::orderBy('date', 'desc')->get();
        return view('admin.thesis.index', compact('theses'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.thesis.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:500',
            'description' => 'nullable|string',
            'sub_by_name' => 'required|string|max:255',
            'sub_by_designation' => 'required|string|max:255',
            'guide_1_name' => 'required|string|max:255',
            'guide_1_designation' => 'required|string|max:255',
            'guide_2_name' => 'nullable|string|max:255',
            'guide_2_designation' => 'nullable|string|max:255',
            'Institution' => 'required|string|max:255',
            'link' => 'nullable|url|max:500',
            'pdf_file' => 'nullable|string|max:255',
            'icon' => 'nullable|string|max:255',
            'date' => 'required|date',
            'show_on_home' => 'boolean',
        ]);

        $data = $request->all();
        $data['show_on_home'] = $request->has('show_on_home');

        ThesisChapter::create($data);

        return redirect()->route('admin.thesis.index')
            ->with('success', 'Thesis/Chapter created successfully');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ThesisChapter $thesis)
    {
        return view('admin.thesis.edit', compact('thesis'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ThesisChapter $thesis)
    {
        $request->validate([
            'name' => 'required|string|max:500',
            'description' => 'nullable|string',
            'sub_by_name' => 'required|string|max:255',
            'sub_by_designation' => 'required|string|max:255',
            'guide_1_name' => 'required|string|max:255',
            'guide_1_designation' => 'required|string|max:255',
            'guide_2_name' => 'nullable|string|max:255',
            'guide_2_designation' => 'nullable|string|max:255',
            'Institution' => 'required|string|max:255',
            'link' => 'nullable|url|max:500',
            'pdf_file' => 'nullable|string|max:255',
            'icon' => 'nullable|string|max:255',
            'date' => 'required|date',
            'show_on_home' => 'boolean',
        ]);

        $data = $request->all();
        $data['show_on_home'] = $request->has('show_on_home');

        $thesis->update($data);

        return redirect()->route('admin.thesis.index')
            ->with('success', 'Thesis/Chapter updated successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ThesisChapter $thesis)
    {
        $thesis->delete();

        return redirect()->route('admin.thesis.index')
            ->with('success', 'Thesis/Chapter deleted successfully');
    }
}
