<x-layout.app page="home">
    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-20">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-4">
                {{ $structure->honorific ?? 'Dr.' }} {{ $structure->name ?? 'Jayanta Debbarma' }}
            </h1>
            <p class="text-xl md:text-2xl mb-6">{{ $structure->short_bio ?? 'Academic Professional' }}</p>
            <p class="text-lg italic mb-8">{{ $structure->moto ?? 'Dedicated to Excellence in Research and Education' }}</p>
            <a href="{{ route('about') }}" class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-300">
                Learn More About Me
            </a>
        </div>
    </section>

    <!-- About Preview Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto text-center">
                <h2 class="text-3xl font-bold text-gray-800 mb-8">About Me</h2>
                <p class="text-lg text-gray-600 leading-relaxed">
                    {{ Str::limit($structure->about ?? 'Welcome to my academic portfolio. Here you will find information about my research, publications, and academic journey.', 300) }}
                </p>
                <a href="{{ route('about') }}" class="inline-block mt-6 text-blue-600 hover:text-blue-800 font-semibold">
                    Read Full Bio →
                </a>
            </div>
        </div>
    </section>

    <!-- Recent Journey Items -->
    @if($journeys->count() > 0)
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center text-gray-800 mb-12">{{ $structure->page3_title ?? 'Education & Experience' }}</h2>
            <div class="grid md:grid-cols-3 gap-8">
                @foreach($journeys as $journey)
                <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition duration-300">
                    <div class="flex items-center mb-4">
                        @if($journey->icon)
                            <img src="{{ asset('asset/icons/' . $journey->icon) }}" alt="Icon" class="w-12 h-12 mr-4">
                        @endif
                        <div>
                            <h3 class="text-xl font-semibold text-gray-800">{{ $journey->title }}</h3>
                            <p class="text-gray-600">{{ $journey->date }}</p>
                        </div>
                    </div>
                    @if($journey->sub_title)
                        <p class="text-gray-600">{{ $journey->sub_title }}</p>
                    @endif
                </div>
                @endforeach
            </div>
            <div class="text-center mt-8">
                <a href="{{ route('journey') }}" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition duration-300">
                    View All Experience
                </a>
            </div>
        </div>
    </section>
    @endif

    <!-- Recent Publications -->
    @if($journals->count() > 0)
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center text-gray-800 mb-12">{{ $structure->page4_title ?? 'Recent Publications' }}</h2>
            <div class="grid md:grid-cols-3 gap-8">
                @foreach($journals as $journal)
                <div class="bg-gray-50 rounded-lg p-6 hover:shadow-lg transition duration-300">
                    <div class="flex items-center mb-4">
                        @if($journal->icon)
                            <img src="{{ asset('asset/icons/' . $journal->icon) }}" alt="Icon" class="w-10 h-10 mr-3">
                        @endif
                        <h3 class="text-lg font-semibold text-gray-800">{{ $journal->name }}</h3>
                    </div>
                    <a href="{{ $journal->link }}" target="_blank" class="text-blue-600 hover:text-blue-800 font-medium">
                        Read Publication →
                    </a>
                </div>
                @endforeach
            </div>
            <div class="text-center mt-8">
                <a href="{{ route('journals') }}" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition duration-300">
                    View All Publications
                </a>
            </div>
        </div>
    </section>
    @endif

    <!-- Key Advisors -->
    @if($advisors->count() > 0)
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center text-gray-800 mb-12">{{ $structure->page6_title ?? 'Key Advisors' }}</h2>
            <div class="grid md:grid-cols-3 gap-8">
                @foreach($advisors as $advisor)
                <div class="bg-white rounded-lg shadow-md p-6 text-center hover:shadow-lg transition duration-300">
                    @if($advisor->image)
                        <img src="{{ asset('asset/images/' . $advisor->image) }}" alt="{{ $advisor->name }}" class="w-24 h-24 rounded-full mx-auto mb-4 object-cover">
                    @endif
                    <h3 class="text-xl font-semibold text-gray-800 mb-2">{{ $advisor->name }}</h3>
                    <p class="text-gray-600 mb-1">{{ $advisor->designation }}</p>
                    <p class="text-gray-500 text-sm">{{ $advisor->rank }}</p>
                    @if($advisor->email)
                        <a href="mailto:{{ $advisor->email }}" class="text-blue-600 hover:text-blue-800 text-sm">
                            {{ $advisor->email }}
                        </a>
                    @endif
                </div>
                @endforeach
            </div>
            <div class="text-center mt-8">
                <a href="{{ route('advisors') }}" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition duration-300">
                    View All Advisors
                </a>
            </div>
        </div>
    </section>
    @endif
</x-layout.app>
