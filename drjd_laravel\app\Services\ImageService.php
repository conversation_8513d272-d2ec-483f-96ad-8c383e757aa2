<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

class ImageService
{
    /**
     * Upload and optimize an image
     */
    public function uploadImage(UploadedFile $file, string $directory = 'images', array $sizes = []): string
    {
        $filename = $this->generateFilename($file);
        $path = $directory . '/' . $filename;
        
        // Create optimized image
        $image = Image::make($file);
        
        // Auto-orient based on EXIF data
        $image->orientate();
        
        // Optimize quality
        $image->encode('jpg', 85);
        
        // Save original
        Storage::disk('public')->put('asset/' . $path, $image->stream());
        
        // Generate different sizes if specified
        foreach ($sizes as $size => $dimensions) {
            $this->createResizedImage($image, $directory, $filename, $size, $dimensions);
        }
        
        return $filename;
    }
    
    /**
     * Create resized version of image
     */
    protected function createResizedImage($image, string $directory, string $filename, string $size, array $dimensions): void
    {
        $resized = clone $image;
        
        if (isset($dimensions['width']) && isset($dimensions['height'])) {
            $resized->fit($dimensions['width'], $dimensions['height']);
        } elseif (isset($dimensions['width'])) {
            $resized->widen($dimensions['width']);
        } elseif (isset($dimensions['height'])) {
            $resized->heighten($dimensions['height']);
        }
        
        $sizedFilename = $this->addSizeToFilename($filename, $size);
        $path = $directory . '/' . $sizedFilename;
        
        Storage::disk('public')->put('asset/' . $path, $resized->stream());
    }
    
    /**
     * Generate unique filename
     */
    protected function generateFilename(UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        $name = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $name = Str::slug($name);
        
        return $name . '_' . time() . '.' . $extension;
    }
    
    /**
     * Add size suffix to filename
     */
    protected function addSizeToFilename(string $filename, string $size): string
    {
        $info = pathinfo($filename);
        return $info['filename'] . '_' . $size . '.' . $info['extension'];
    }
    
    /**
     * Delete image and its variants
     */
    public function deleteImage(string $filename, string $directory = 'images', array $sizes = []): bool
    {
        $deleted = true;
        
        // Delete original
        if (Storage::disk('public')->exists('asset/' . $directory . '/' . $filename)) {
            $deleted = Storage::disk('public')->delete('asset/' . $directory . '/' . $filename);
        }
        
        // Delete sized versions
        foreach ($sizes as $size => $dimensions) {
            $sizedFilename = $this->addSizeToFilename($filename, $size);
            if (Storage::disk('public')->exists('asset/' . $directory . '/' . $sizedFilename)) {
                Storage::disk('public')->delete('asset/' . $directory . '/' . $sizedFilename);
            }
        }
        
        return $deleted;
    }
    
    /**
     * Get image URL
     */
    public function getImageUrl(string $filename, string $directory = 'images', string $size = null): string
    {
        if ($size) {
            $filename = $this->addSizeToFilename($filename, $size);
        }
        
        return asset('asset/' . $directory . '/' . $filename);
    }
    
    /**
     * Check if image exists
     */
    public function imageExists(string $filename, string $directory = 'images'): bool
    {
        return Storage::disk('public')->exists('asset/' . $directory . '/' . $filename);
    }
    
    /**
     * Generate WebP version of image
     */
    public function generateWebP(string $filename, string $directory = 'images'): string
    {
        $originalPath = 'asset/' . $directory . '/' . $filename;
        
        if (!Storage::disk('public')->exists($originalPath)) {
            throw new \Exception('Original image not found');
        }
        
        $image = Image::make(Storage::disk('public')->path($originalPath));
        $webpFilename = pathinfo($filename, PATHINFO_FILENAME) . '.webp';
        $webpPath = 'asset/' . $directory . '/' . $webpFilename;
        
        // Convert to WebP with 85% quality
        $image->encode('webp', 85);
        Storage::disk('public')->put($webpPath, $image->stream());
        
        return $webpFilename;
    }
    
    /**
     * Create progressive JPEG
     */
    public function createProgressiveJpeg(string $filename, string $directory = 'images'): string
    {
        $originalPath = 'asset/' . $directory . '/' . $filename;
        
        if (!Storage::disk('public')->exists($originalPath)) {
            throw new \Exception('Original image not found');
        }
        
        $image = Image::make(Storage::disk('public')->path($originalPath));
        
        // Create low quality version for progressive loading
        $lowQualityFilename = $this->addSizeToFilename($filename, 'low');
        $lowQualityPath = 'asset/' . $directory . '/' . $lowQualityFilename;
        
        $lowQualityImage = clone $image;
        $lowQualityImage->blur(2)->encode('jpg', 20);
        Storage::disk('public')->put($lowQualityPath, $lowQualityImage->stream());
        
        return $lowQualityFilename;
    }
    
    /**
     * Get image dimensions
     */
    public function getImageDimensions(string $filename, string $directory = 'images'): array
    {
        $path = 'asset/' . $directory . '/' . $filename;
        
        if (!Storage::disk('public')->exists($path)) {
            return ['width' => 0, 'height' => 0];
        }
        
        $image = Image::make(Storage::disk('public')->path($path));
        
        return [
            'width' => $image->width(),
            'height' => $image->height()
        ];
    }
}
