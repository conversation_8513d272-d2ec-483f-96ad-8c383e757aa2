<x-layout.admin title="Add PDF/Document">
    <div class="mb-8">
        <div class="flex items-center space-x-4">
            <a href="{{ route('admin.field-interest-pdf.index') }}" class="text-gray-600 hover:text-gray-800">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Add PDF/Document</h1>
                <p class="text-gray-600">Add a new research paper, document, or publication</p>
            </div>
        </div>
    </div>

    <form method="POST" action="{{ route('admin.field-interest-pdf.store') }}" class="space-y-6">
        @csrf
        
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-6">Document Details</h2>
            
            <div class="grid gap-6">
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Title *</label>
                    <input type="text" id="title" name="title" required
                           value="{{ old('title') }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                           placeholder="Enter document title">
                    @error('title')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea id="description" name="description" rows="3"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                              placeholder="Brief description of the document">{{ old('description') }}</textarea>
                    @error('description')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <label for="pdf_file" class="block text-sm font-medium text-gray-700 mb-2">PDF Filename *</label>
                        <input type="text" id="pdf_file" name="pdf_file" required
                               value="{{ old('pdf_file') }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                               placeholder="e.g., research-paper.pdf">
                        <p class="text-sm text-gray-500 mt-1">Upload the PDF file to /public/asset/pdfs/ directory first</p>
                        @error('pdf_file')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label for="thumbnail" class="block text-sm font-medium text-gray-700 mb-2">Thumbnail Image</label>
                        <input type="text" id="thumbnail" name="thumbnail"
                               value="{{ old('thumbnail') }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                               placeholder="e.g., thumbnail.jpg">
                        <p class="text-sm text-gray-500 mt-1">Upload the thumbnail to /public/asset/images/ directory first</p>
                        @error('thumbnail')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
                
                <div>
                    <label for="display_date" class="block text-sm font-medium text-gray-700 mb-2">Display Date *</label>
                    <input type="date" id="display_date" name="display_date" required
                           value="{{ old('display_date') }}"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                    <p class="text-sm text-gray-500 mt-1">This date will be used for sorting and display</p>
                    @error('display_date')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div class="flex items-center">
                    <input type="checkbox" id="show_on_home" name="show_on_home" value="1"
                           {{ old('show_on_home') ? 'checked' : '' }}
                           class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                    <label for="show_on_home" class="ml-2 block text-sm text-gray-700">
                        Show on home page (maximum 4 items will be displayed)
                    </label>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex items-center justify-between">
            <a href="{{ route('admin.field-interest-pdf.index') }}" 
               class="bg-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-400 transition duration-300">
                Cancel
            </a>
            
            <button type="submit" 
                    class="bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition duration-300">
                <i class="fas fa-save mr-2"></i>
                Save PDF/Document
            </button>
        </div>
    </form>
</x-layout.admin>
