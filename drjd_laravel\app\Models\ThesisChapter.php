<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ThesisChapter extends Model
{
    protected $table = 'thesis&chapters';
    public $timestamps = false;

    protected $fillable = [
        'icon',
        'name',
        'sub_by_name',
        'sub_by_designation',
        'guide_1_name',
        'guide_1_designation',
        'guide_2_name',
        'guide_2_designation',
        'Institution',
        'link',
        'pdf_file',
        'description',
        'date',
        'show_on_home',
    ];

    protected $casts = [
        'show_on_home' => 'boolean',
        'date' => 'date',
    ];

    public static function getHomeItems($limit = 4)
    {
        return self::where('show_on_home', true)
                   ->orderBy('date', 'desc')
                   ->limit($limit)
                   ->get();
    }

    public static function getOtherItems()
    {
        return self::where('show_on_home', false)
                   ->orderBy('date', 'desc')
                   ->get();
    }
}
