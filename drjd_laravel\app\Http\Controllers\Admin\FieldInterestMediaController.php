<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\FieldInterestMedia;

class FieldInterestMediaController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $media = FieldInterestMedia::orderBy('display_date', 'desc')->get();
        return view('admin.field-interest-media.index', compact('media'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.field-interest-media.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'media_type' => 'required|in:image,video',
            'file_path' => 'required|string|max:255',
            'thumbnail' => 'nullable|string|max:255',
            'display_date' => 'required|date',
            'show_on_home' => 'boolean',
        ]);

        $data = $request->all();
        $data['upload_date'] = now();
        $data['show_on_home'] = $request->has('show_on_home');

        FieldInterestMedia::create($data);

        return redirect()->route('admin.field-interest-media.index')
            ->with('success', 'Media item created successfully');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(FieldInterestMedia $fieldInterestMedium)
    {
        return view('admin.field-interest-media.edit', compact('fieldInterestMedium'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, FieldInterestMedia $fieldInterestMedium)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'media_type' => 'required|in:image,video',
            'file_path' => 'required|string|max:255',
            'thumbnail' => 'nullable|string|max:255',
            'display_date' => 'required|date',
            'show_on_home' => 'boolean',
        ]);

        $data = $request->all();
        $data['show_on_home'] = $request->has('show_on_home');

        $fieldInterestMedium->update($data);

        return redirect()->route('admin.field-interest-media.index')
            ->with('success', 'Media item updated successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(FieldInterestMedia $fieldInterestMedium)
    {
        $fieldInterestMedium->delete();

        return redirect()->route('admin.field-interest-media.index')
            ->with('success', 'Media item deleted successfully');
    }
}
