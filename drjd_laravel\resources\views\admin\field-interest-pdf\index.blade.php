<x-layout.admin title="PDFs & Documents">
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">PDFs & Documents</h1>
                <p class="text-gray-600">Manage your research papers, documents, and publications</p>
            </div>
            <a href="{{ route('admin.field-interest-pdf.create') }}" 
               class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition duration-300">
                <i class="fas fa-plus mr-2"></i>
                Add PDF/Document
            </a>
        </div>
    </div>

    @if($pdfs->count() > 0)
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Thumbnail
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Title
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Date
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Home Page
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            PDF File
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($pdfs as $pdf)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($pdf->thumbnail)
                                    <div class="flex items-center">
                                        <img src="{{ asset('asset/images/' . $pdf->thumbnail) }}" 
                                             alt="Thumbnail" class="h-12 w-12 rounded-lg object-cover border"
                                             onerror="this.src='{{ asset('asset/images/default-pdf.png') }}'">
                                    </div>
                                @else
                                    <div class="h-12 w-12 bg-gray-200 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-file-pdf text-gray-400 text-xl"></i>
                                    </div>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900 max-w-md">{{ $pdf->title }}</div>
                                @if($pdf->description)
                                    <div class="text-sm text-gray-500 max-w-md truncate">{{ $pdf->description }}</div>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ $pdf->display_date->format('M d, Y') }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($pdf->show_on_home)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-home mr-1"></i>
                                        Yes
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        No
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                @if($pdf->pdf_file)
                                    <a href="{{ asset('asset/pdfs/' . $pdf->pdf_file) }}" target="_blank" 
                                       class="text-indigo-600 hover:text-indigo-900 text-sm">
                                        <i class="fas fa-external-link-alt mr-1"></i>
                                        View PDF
                                    </a>
                                @else
                                    <span class="text-gray-400 text-sm">No file</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex items-center justify-end space-x-2">
                                    <a href="{{ route('admin.field-interest-pdf.edit', $pdf) }}" 
                                       class="text-indigo-600 hover:text-indigo-900">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form method="POST" action="{{ route('admin.field-interest-pdf.destroy', $pdf) }}" 
                                          class="inline" onsubmit="return confirm('Are you sure you want to delete this PDF?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="text-red-600 hover:text-red-900">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        
        <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-info-circle text-blue-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">Home Page Display</h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <p>Items marked "Yes" will appear on the home page (maximum 4 items). Remaining items will be shown on the "View More" page.</p>
                    </div>
                </div>
            </div>
        </div>
    @else
        <div class="bg-white rounded-lg shadow p-6 text-center">
            <div class="text-gray-500 mb-4">
                <i class="fas fa-file-pdf text-4xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No PDFs or Documents Found</h3>
            <p class="text-gray-600 mb-4">Start adding your research papers, documents, and publications.</p>
            <a href="{{ route('admin.field-interest-pdf.create') }}" 
               class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition duration-300">
                <i class="fas fa-plus mr-2"></i>
                Add First PDF/Document
            </a>
        </div>
    @endif
</x-layout.admin>
