<?php
// Include database connection and optimization functions
include 'db_connect.php';
include 'src/db_optimize.php';

$table_name = 'structure'; // Table for existing data
$id = 1; // Example: Extract data for id = 1
$data = [];
$error = '';

// Use cached prepared query for structure data
try {
  $data = cached_prepared_query(
    $conn,
    "SELECT * FROM $table_name WHERE id = ?",
    "i",
    [$id],
    'structure_data_' . $id,
    3600 // Cache for 1 hour
  );

  if (empty($data)) {
    $error = "No data found for ID $id in table '$table_name'.";
  } else {
    $data = $data[0]; // Get first row since we're querying by primary key
  }
} catch (Exception $e) {
  $error = "Error fetching data: " . $e->getMessage();
}

// Fetch data from journey table with caching
$journey_data = [];
try {
  $journey_data = cached_query(
    $conn,
    "SELECT * FROM journey ORDER BY date DESC",
    'journey_data',
    1800 // Cache for 30 minutes
  );

  if (empty($journey_data)) {
    $error .= " No data found in 'journey' table.";
  }
} catch (Exception $e) {
  $error .= " Error fetching journey data: " . $e->getMessage();
}

// Fetch data from journey_images table with caching
$journey_images = [];
try {
  $journey_images = cached_query(
    $conn,
    "SELECT * FROM journey_images ORDER BY date DESC",
    'journey_images_data',
    1800 // Cache for 30 minutes
  );

  if (empty($journey_images)) {
    $error = "No data found in 'journey_images' table.";
  }
} catch (Exception $e) {
  $error = "Error fetching journey_images data: " . $e->getMessage();
}

// Fetch data from journals&papers table with caching
$journals_papers = [];
try {
  $journals_papers = cached_query(
    $conn,
    "SELECT * FROM `journals&papers` ORDER BY id ASC",
    'journals_papers_data',
    3600 // Cache for 1 hour
  );

  if (empty($journals_papers)) {
    $error = "No data found in 'journals&papers' table.";
  }
} catch (Exception $e) {
  $error = "Error fetching journals&papers data: " . $e->getMessage();
}

// Fetch data from thesis&chapters table with caching
$thesis_chapters = [];
try {
  $thesis_chapters = cached_query(
    $conn,
    "SELECT * FROM `thesis&chapters` ORDER BY id ASC",
    'thesis_chapters_data',
    3600 // Cache for 1 hour
  );

  if (empty($thesis_chapters)) {
    $error = "No data found in 'thesis&chapters' table.";
  }
} catch (Exception $e) {
  $error = "Error fetching thesis&chapters data: " . $e->getMessage();
}

// Fetch data from advisors table with caching
$advisors = [];
try {
  $advisors = cached_query(
    $conn,
    "SELECT * FROM advisors ORDER BY id ASC",
    'advisors_data',
    7200 // Cache for 2 hours
  );

  if (empty($advisors)) {
    $error = "No data found in 'advisors' table.";
  }
} catch (Exception $e) {
  $error = "Error fetching advisors data: " . $e->getMessage();
}

// Fetch data from field_interest table with caching
$field_interests = [];
try {
  $field_interests = cached_query(
    $conn,
    "SELECT * FROM field_interest ORDER BY id DESC LIMIT 4",
    'field_interests_data',
    1800 // Cache for 30 minutes
  );
} catch (Exception $e) {
  $error .= " Error fetching field of interest data: " . $e->getMessage();
}

// Fetch PDFs for Field of Interest section with caching
$pdfs = [];
try {
  $pdfs = cached_query(
    $conn,
    "SELECT * FROM field_interest_pdfs ORDER BY upload_date DESC LIMIT 4",
    'field_interest_pdfs_data',
    1800 // Cache for 30 minutes
  );
} catch (Exception $e) {
  $error .= " Error fetching PDFs data: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Dr. Jayanta Debbarma - Groundwater Researcher</title>
  <meta name="description" content="Dr. Jayanta Debbarma is a distinguished groundwater researcher from Tripura, India, specializing in groundwater potential mapping, spring identification, and sustainable water management in Tripura." />
  <meta name="keywords" content="Jayanta Debbarma, Dr. Jayanta Debbarma, groundwater Tripura, groundwater research, water management Tripura" />
  <meta name="author" content="Dr. Jayanta Debbarma" />

  <!-- DNS prefetch for external domains -->
  <link rel="dns-prefetch" href="https://cdn.jsdelivr.net">
  <link rel="dns-prefetch" href="https://cdnjs.cloudflare.com">

  <!-- Preconnect to critical domains -->
  <link rel="preconnect" href="https://cdn.jsdelivr.net" crossorigin>
  <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>

  <!-- Favicon -->
  <link
    rel="shortcut icon"
    href="asset/images/fabicon.png"
    type="image/x-icon" />

  <!-- Preload critical assets -->
  <link rel="preload" href="asset/images/main.png" as="image" type="image/png">
  <link rel="preload" href="src/critical.css" as="style">

  <!-- Critical CSS inline -->
  <style>
    <?php include 'src/critical.css'; ?>

    /* Mobile-specific styles */
    html {
      scroll-behavior: smooth;
      font-size: 16px;
    }

    body {
      overflow-x: hidden;
      width: 100%;
      max-width: 100vw;
    }

    section {
      min-height: auto;
      padding: 2rem 0;
      margin-bottom: 1rem;
      scroll-margin-top: 10vh;
    }

    /* Mobile-optimized card sizes */
    :root {
      --card-width-large: 90vw;
      --card-height-large: calc(90vw * 1.2);
      --card-width-small: 45vw;
      --card-height-small: calc(45vw * 1.2);
    }

    /* Mobile navigation */
    #mobile-nav {
      position: fixed;
      top: 0;
      right: 0;
      height: 100vh;
      width: 80%;
      background-color: white;
      z-index: 1000;
      transform: translateX(100%);
      transition: transform 0.3s ease-in-out;
      box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
      padding: 2rem;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
    }

    #mobile-nav a {
      padding: 1rem 0;
      border-bottom: 1px solid #f0f0f0;
      font-size: 1.2rem;
    }

    #mobile-menu-button {
      display: block;
      cursor: pointer;
      z-index: 1001;
    }

    .mobile-nav-active {
      transform: translateX(0) !important;
    }

    .mobile-menu-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 999;
      display: none;
    }

    .mobile-menu-overlay.active {
      display: block;
    }

    /* Mobile layout adjustments */
    .flex-col-mobile {
      flex-direction: column !important;
    }

    .w-full-mobile {
      width: 100% !important;
    }

    .h-auto-mobile {
      height: auto !important;
    }

    .mb-mobile-5 {
      margin-bottom: 1.25rem !important;
    }

    .px-mobile-4 {
      padding-left: 1rem !important;
      padding-right: 1rem !important;
    }

    .py-mobile-4 {
      padding-top: 1rem !important;
      padding-bottom: 1rem !important;
    }

    .text-center-mobile {
      text-align: center !important;
    }

    .justify-center-mobile {
      justify-content: center !important;
    }

    .items-center-mobile {
      align-items: center !important;
    }

    .gap-mobile-4 {
      gap: 1rem !important;
    }

    /* Hover effect styles */
    .item-hover {
      position: relative;
    }

    .overlay-content {
      position: absolute;
      inset: 0;
      background-color: rgba(0, 0, 0, 0.6);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;
      border-radius: 0.5rem;
      z-index: 5;
    }

    .video-play-button {
      position: absolute;
      inset: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
    }

    .item-hover:hover .overlay-content,
    .item-hover:active .overlay-content {
      opacity: 1;
    }

    /* Progressive image loading styles */
    .img-blur {
      filter: blur(15px);
      transform: scale(1.05);
      transition: filter 0.5s ease-out, transform 0.5s ease-out;
      will-change: filter, transform;
    }

    .img-loaded {
      filter: blur(0);
      transform: scale(1);
    }

    /* Placeholder style for images before they load */
    img.lazy {
      background-color: #f0f0f0;
      transition: filter 0.5s ease-in-out, opacity 0.3s ease;
      min-height: 50px;
      opacity: 0.8;
    }

    /* Journey image cards */
    .journey-card-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 0.5rem;
    }

    /* Text truncation for journey cards */
    .line-clamp-1 {
      display: -webkit-box;
      -webkit-line-clamp: 1;
      line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .line-clamp-2 {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  </style>

  <!-- Defer non-critical CSS -->
  <link rel="preload" href="src/output.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <noscript><link rel="stylesheet" href="src/output.css"></noscript>
</head>

<body>
  <!-- Navbar (Sticky) -->
  <nav class="w-full flex justify-between items-center px-4 h-[10vh] border-b-[1px] bg-white z-100 sticky top-0">
    <a
      id="nav-logo"
      class="font-extrabold text-2xl cursor-pointer drop-shadow-md"
      href="#home">
      <span class="text-blue-500"><?php echo htmlspecialchars($data['honorific'] ?? 'Dr'); ?></span> <?php echo htmlspecialchars($data['name'] ?? 'Jayanta Debbarma'); ?>
    </a>

    <!-- Mobile Menu Button -->
    <div id="mobile-menu-button" class="flex flex-col justify-center items-center p-2">
      <div class="w-8 h-1 bg-blue-500 rounded-full mb-2"></div>
      <div class="w-8 h-1 bg-blue-500 rounded-full mb-2"></div>
      <div class="w-8 h-1 bg-blue-500 rounded-full"></div>
    </div>
  </nav>

  <!-- Mobile Navigation Menu -->
  <div id="mobile-nav">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-xl font-bold">Menu</h2>
      <div id="mobile-close" class="text-3xl cursor-pointer">×</div>
    </div>
    <a href="#home" class="block cursor-pointer hover:text-blue-400"><?php echo htmlspecialchars($data['page1'] ?? 'Home'); ?></a>
    <a href="#about" class="block cursor-pointer hover:text-blue-400"><?php echo htmlspecialchars($data['page2'] ?? 'About'); ?></a>
    <a href="#journey" class="block cursor-pointer hover:text-blue-400"><?php echo htmlspecialchars($data['page3'] ?? 'Journey'); ?></a>
    <a href="#journals" class="block cursor-pointer hover:text-blue-400"><?php echo htmlspecialchars($data['page4'] ?? 'Journals'); ?></a>
    <a href="#thesis" class="block cursor-pointer hover:text-blue-400"><?php echo htmlspecialchars($data['page5'] ?? 'Thesis'); ?></a>
    <a href="#advisor" class="block cursor-pointer hover:text-blue-400"><?php echo htmlspecialchars($data['page6'] ?? 'Advisors'); ?></a>
    <a href="#interest" class="block cursor-pointer hover:text-blue-400"><?php echo htmlspecialchars($data['page7'] ?? 'Field of Interest'); ?></a>
    <a href="#contact" class="block cursor-pointer hover:text-blue-400"><?php echo htmlspecialchars($data['page8'] ?? 'Contact'); ?></a>
  </div>

  <!-- Mobile Menu Overlay -->
  <div class="mobile-menu-overlay"></div>

  <!-- Main Content -->
  <main class="px-4">
    <!-- Home Section - Mobile Optimized -->
    <section id="home" class="w-full flex flex-col items-center justify-center py-8">
      <div class="w-full flex flex-col items-center justify-center gap-3 mb-6">
        <div class="flex flex-col items-center gap-1.5 text-center">
          <h1 class="font-extrabold text-4xl">
            <span class="text-blue-500"><?php echo htmlspecialchars($data['honorific'] ?? 'Dr'); ?></span> <?php echo htmlspecialchars($data['name'] ?? 'Jayanta Debbarma'); ?>
          </h1>
          <h5 class="font-bold text-sm"><?php echo htmlspecialchars($data['short_bio'] ?? '(Groundwater Researcher, Superintending Engineer PWD(W.R))'); ?></h5>
        </div>
        <p class="text-center">
          <?php echo htmlspecialchars($data['moto'] ?? 'Dedicated to Preserving Earth\'s Groundwater for Generations to Come.'); ?>
        </p>
      </div>
      <div class="w-full relative flex justify-center">
        <!-- Blue Background Shape -->
        <div
          class="w-[80vw] h-[80vw] bg-blue-500 absolute z-0 rounded-3xl blur-3xl opacity-50"
          id="blured-bg"></div>
        <!-- Image -->
        <img
          data-src="asset/images/main.png"
          src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
          class="w-[90%] max-w-[350px] relative z-10 lazy"
          alt="Dr. Jayanta Debbarma" />
      </div>
    </section>

    <!-- About Section - Mobile Optimized -->
    <section id="about" class="w-full flex flex-col items-center py-8">
      <div class="w-full flex justify-center mb-6">
        <div class="relative w-[80vw] h-[80vw] max-w-[350px] max-h-[350px] bg-blue-500 rounded-2xl">
          <img
            id="about-image"
            class="w-full h-full object-cover absolute top-[5%] left-[5%] rounded-2xl drop-shadow-md lazy"
            data-src="asset/images/page1.jpg"
            src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
            alt="Dr. Jayanta Debbarma" />
        </div>
      </div>
      <div class="w-full flex flex-col items-center gap-5 text-center">
        <h1 class="font-extrabold text-3xl">
          Meet<br /><span class="text-blue-500"><?php echo htmlspecialchars($data['honorific'] ?? 'Dr'); ?></span> <?php echo htmlspecialchars($data['name'] ?? 'Jayanta Debbarma'); ?>
        </h1>
        <p id="intro-text" class="text-sm">
          <?php echo nl2br(htmlspecialchars($data['about'] ?? 'Dr. Jayanta Debbarma is a distinguished groundwater researcher from Tripura, India, specializing in groundwater potential mapping, spring identification, and sustainable water management in Tripura.')); ?>
        </p>
      </div>
    </section>

    <!-- Journey Section - Mobile Optimized -->
    <section id="journey" class="w-full flex flex-col items-center py-8">
      <div class="w-full bg-blue-500 py-4 mb-6 rounded-lg text-center">
        <h2 class="text-2xl font-bold text-white"><?php echo htmlspecialchars($data['page3_title'] ?? 'Education & Experience'); ?></h2>
      </div>

      <div class="w-full flex flex-col gap-4">
        <?php foreach ($journey_data as $journey): ?>
          <div class="w-full py-3 bg-gray-100 flex items-center gap-4 px-4 rounded-lg shadow-md">
            <img
              src="asset/icons/<?php echo file_exists('asset/icons/' . $journey['icon']) ? htmlspecialchars($journey['icon']) : 'default.png'; ?>"
              alt="<?php echo htmlspecialchars($journey['title'] ?? 'Icon'); ?>"
              class="w-12 h-12"
              onerror="this.src='asset/icons/default.png'; this.onerror=null;" />
            <div class="flex flex-col">
              <h3 class="text-lg font-bold">
                <?php echo htmlspecialchars($journey['title'] ?? 'Untitled'); ?>
              </h3>
              <div class="flex flex-col">
                <?php if (!empty($journey['sub_title'])): ?>
                  <p class="font-bold text-blue-900 text-sm">
                    <?php echo htmlspecialchars($journey['sub_title']); ?>
                  </p>
                <?php endif; ?>
                <p class="text-sm"><?php echo htmlspecialchars($journey['date'] ?? 'Unknown date'); ?></p>
              </div>
            </div>
          </div>
        <?php endforeach; ?>
        <?php if (empty($journey_data)): ?>
          <p class="text-red-500 text-center">No journey data available.</p>
        <?php endif; ?>
      </div>

      <!-- Journey Images - Mobile Grid Layout -->
      <div class="w-full mt-8 grid grid-cols-2 gap-4">
        <?php
        $count = 0;
        foreach ($journey_images as $image) {
          if ($image['image_for'] === 'largePic') {
            // Large card - takes full width
        ?>
            <div class="col-span-2 aspect-square bg-white rounded-lg shadow-lg p-2 flex flex-col justify-center">
              <div class="w-full h-[70%] flex items-center justify-center">
                <img
                  class="journey-card-image lazy"
                  data-src="asset/images/<?php echo htmlspecialchars($image['image_name']); ?>"
                  src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
                  alt="<?php echo htmlspecialchars($image['name'] ?? 'Journey Image'); ?>"
                />
              </div>
              <!-- Title and date in the empty space -->
              <div class="w-full h-[30%] flex justify-around items-center p-1">
                <div class="text-center">
                  <h4 class="text-sm font-bold text-blue-500 line-clamp-1"><?php echo htmlspecialchars($image['name'] ?? ''); ?></h4>
                  <p class="text-xs text-gray-600"><?php echo htmlspecialchars($image['date'] ?? ''); ?></p>
                </div>
              </div>
            </div>
            <?php
          } else {
            // Mini card
            ?>
            <div class="aspect-square bg-white rounded-lg shadow-lg p-2 flex flex-col justify-center">
              <div class="w-full h-[65%] flex items-center justify-center">
                <img
                  class="journey-card-image lazy"
                  data-src="asset/images/<?php echo htmlspecialchars($image['image_name']); ?>"
                  src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
                  alt="<?php echo htmlspecialchars($image['name'] ?? 'Journey Image'); ?>"
                />
              </div>
              <!-- Title and date in the empty space -->
              <div class="w-full h-[35%] flex justify-around items-center p-1">
                <div class="text-center">
                  <h4 class="text-xs font-bold text-blue-500 line-clamp-1"><?php echo htmlspecialchars($image['name'] ?? ''); ?></h4>
                  <p class="text-[10px] text-gray-600"><?php echo htmlspecialchars($image['date'] ?? ''); ?></p>
                </div>
              </div>
            </div>
            <?php
          }
          $count++;
        }
        ?>
      </div>
    </section>

    <!-- Journals Section - Mobile Optimized -->
    <section id="journals" class="w-full flex flex-col items-center py-8">
      <div class="w-full bg-blue-500 py-4 mb-6 rounded-lg text-center">
        <h2 class="text-2xl font-bold text-white"><?php echo htmlspecialchars($data['page4_title'] ?? 'Journals & Papers'); ?></h2>
      </div>

      <div class="w-full flex flex-col gap-3">
        <?php foreach ($journals_papers as $paper): ?>
          <a
            class="flex items-center space-x-3 px-4 py-3 rounded-lg bg-gray-100 shadow-md gap-2 w-full cursor-pointer"
            href="<?php echo htmlspecialchars($paper['link']); ?>"
            target="_blank"
          >
            <img
              src="asset/icons/<?php echo htmlspecialchars($paper['icon']); ?>"
              alt="Paper Icon"
              class="w-6 h-6"
            />
            <p class="font-semibold text-sm">
              <?php echo htmlspecialchars($paper['name']); ?>
            </p>
          </a>
        <?php endforeach; ?>
        <?php if (empty($journals_papers)): ?>
          <p class="text-red-500 text-center"><?php echo htmlspecialchars($error); ?></p>
        <?php endif; ?>
      </div>
    </section>

    <!-- Thesis Section - Mobile Optimized -->
    <section id="thesis" class="w-full flex flex-col items-center py-8">
      <div class="w-full bg-blue-500 py-4 mb-6 rounded-lg text-center">
        <h2 class="text-2xl font-bold text-white"><?php echo htmlspecialchars($data['page5_title'] ?? 'Thesis & Chapters'); ?></h2>
      </div>

      <div class="w-full flex flex-col gap-6">
        <?php foreach ($thesis_chapters as $thesis): ?>
          <a
            class="flex flex-col justify-around text-center items-center px-4 py-4 rounded-lg bg-gray-100 shadow-md gap-3 cursor-pointer w-full"
            <?php if (!empty($thesis['link'])): ?>
              href="<?php echo htmlspecialchars($thesis['link']); ?>"
              target="_blank"
            <?php endif; ?>
          >
            <div class="flex flex-col justify-between items-center gap-2">
              <img
                src="asset/icons/<?php echo htmlspecialchars($thesis['icon']); ?>"
                alt="Paper Icon"
                class="w-10 h-10"
              />
              <p class="font-bold">
                <?php echo htmlspecialchars($thesis['name']); ?>
              </p>
            </div>
            <p class="text-sm">
              Thesis Submitted By <br /><span class="font-bold"><?php echo htmlspecialchars($thesis['sub_by_name']); ?></span>
              <?php if (!empty($thesis['sub_by_designation'])): ?>
                <br /><?php echo htmlspecialchars($thesis['sub_by_designation']); ?>
              <?php endif; ?>
            </p>
            <?php if (!empty($thesis['guide_1_name']) || !empty($thesis['guide_2_name'])): ?>
              <p class="text-sm">
                Under the guidance of <br />
                <?php if (!empty($thesis['guide_1_name'])): ?>
                  <span class="font-bold"><?php echo htmlspecialchars($thesis['guide_1_name']); ?></span> <br />
                  <?php echo htmlspecialchars($thesis['guide_1_designation']); ?><br />
                <?php endif; ?>
                <?php if (!empty($thesis['guide_2_name'])): ?>
                  <span class="font-bold"><?php echo htmlspecialchars($thesis['guide_2_name']); ?></span> <br />
                  <?php echo htmlspecialchars($thesis['guide_2_designation']); ?>
                <?php endif; ?>
              </p>
            <?php endif; ?>
            <p class="font-bold text-blue-900 text-sm">
              <?php echo htmlspecialchars($thesis['Institution']); ?>
            </p>
          </a>
        <?php endforeach; ?>
        <?php if (empty($thesis_chapters)): ?>
          <p class="text-red-500 text-center"><?php echo htmlspecialchars($error); ?></p>
        <?php endif; ?>
      </div>
    </section>

    <!-- Advisor's Section - Mobile Optimized -->
    <section id="advisor" class="w-full flex flex-col items-center py-8">
      <div class="w-full bg-blue-500 py-4 mb-6 rounded-lg text-center">
        <h2 class="text-2xl font-bold text-white"><?php echo htmlspecialchars($data['page6_title'] ?? 'Key Advisors'); ?></h2>
      </div>

      <div class="w-full flex flex-col gap-6">
        <?php foreach ($advisors as $advisor): ?>
          <div class="w-full rounded-lg flex flex-col items-center bg-gray-200 shadow-lg p-4">
            <img
              data-src="asset/images/<?php echo htmlspecialchars($advisor['image']); ?>"
              src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
              alt="<?php echo htmlspecialchars($advisor['name']); ?>"
              class="bg-white w-32 h-32 rounded-full object-cover lazy mb-3"
            />
            <div class="w-full text-center flex flex-col gap-1">
              <h3 class="font-bold text-xl"><?php echo htmlspecialchars($advisor['name']); ?></h3>
              <p class="text-sm"><?php echo htmlspecialchars($advisor['designation']); ?></p>
              <h4 class="font-bold text-lg"><?php echo htmlspecialchars($advisor['rank']); ?></h4>
              <?php if (!empty($advisor['email'])): ?>
                <h4 class="font-bold text-sm break-words">
                  <a href="mailto:<?php echo htmlspecialchars($advisor['email']); ?>">
                    <?php echo htmlspecialchars($advisor['email']); ?>
                  </a>
                </h4>
              <?php endif; ?>
            </div>
          </div>
        <?php endforeach; ?>
        <?php if (empty($advisors)): ?>
          <p class="text-red-500 text-center"><?php echo htmlspecialchars($error); ?></p>
        <?php endif; ?>
      </div>
    </section>

    <!-- Interest Section - Mobile Optimized -->
    <section id="interest" class="w-full flex flex-col items-center py-8">
      <div class="w-full bg-blue-500 py-4 mb-6 rounded-lg text-center">
        <h2 class="text-2xl font-bold text-white"><?php echo htmlspecialchars($data['page7_title'] ?? 'Field of Interest'); ?></h2>
      </div>

      <!-- PDFs Section -->
      <div class="w-full mb-8">
        <h3 class="text-xl font-bold mb-4 text-center">PDFs & Documents</h3>

        <div class="grid grid-cols-2 gap-3">
          <?php if (!empty($pdfs)): ?>
            <?php foreach (array_slice($pdfs, 0, 4) as $index => $pdf): ?>
              <div class="aspect-square bg-black rounded-lg relative overflow-hidden cursor-pointer item-hover">
                <a href="asset/pdfs/<?php echo htmlspecialchars($pdf['pdf_file']); ?>" target="_blank" class="block w-full h-full">
                  <img
                    class="w-full h-full object-cover rounded-lg lazy"
                    data-src="asset/thumbnails/<?php echo htmlspecialchars($pdf['thumbnail']); ?>"
                    src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
                    alt="<?php echo htmlspecialchars($pdf['title']); ?>" />
                  <!-- Hover overlay with title -->
                  <div class="overlay-content">
                    <div class="text-white text-center p-2">
                      <p class="font-bold text-sm"><?php echo htmlspecialchars($pdf['title']); ?></p>
                      <span class="text-xs"><?php echo date('m/d/y', strtotime($pdf['display_date'] ?? $pdf['upload_date'])); ?></span>
                    </div>
                  </div>
                </a>
              </div>
            <?php endforeach; ?>
          <?php else: ?>
            <!-- Placeholder PDFs if none are available -->
            <div class="aspect-square bg-black rounded-lg relative overflow-hidden cursor-pointer item-hover">
              <a href="#" class="block w-full h-full">
                <img
                  class="w-full h-full object-cover rounded-lg lazy"
                  data-src="asset/images/pdf1.png"
                  src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
                  alt="Sample PDF" />
                <div class="overlay-content">
                  <div class="text-white text-center p-2">
                    <p class="font-bold text-sm">Sample PDF 1</p>
                    <span class="text-xs">Click to view</span>
                  </div>
                </div>
              </a>
            </div>
            <!-- Add more placeholder PDFs as needed -->
          <?php endif; ?>
        </div>

        <div class="w-full flex justify-center mt-3">
          <a href="field_interest_pdfs.php" class="px-4 py-2 bg-blue-500 text-white font-bold rounded-lg cursor-pointer hover:bg-blue-600">View More</a>
        </div>
      </div>

      <!-- Media Section -->
      <div class="w-full">
        <h3 class="text-xl font-bold mb-4 text-center">Images & Videos</h3>

        <div class="grid grid-cols-2 gap-3">
          <?php if (!empty($media_items)): ?>
            <?php foreach (array_slice($media_items, 0, 4) as $index => $media): ?>
              <div class="aspect-square bg-black rounded-lg relative overflow-hidden cursor-pointer item-hover">
                <?php if ($media['media_type'] === 'image'): ?>
                  <a href="asset/media/<?php echo htmlspecialchars($media['file_path']); ?>" target="_blank" class="block w-full h-full">
                    <img
                      class="w-full h-full object-cover rounded-lg lazy"
                      src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
                      data-src="asset/media/<?php echo htmlspecialchars($media['file_path']); ?>"
                      alt="<?php echo htmlspecialchars($media['title']); ?>" />
                    <!-- Hover overlay with title -->
                    <div class="overlay-content">
                      <div class="text-white text-center p-2">
                        <p class="font-bold text-sm"><?php echo htmlspecialchars($media['title']); ?></p>
                        <span class="text-xs"><?php echo date('m/d/y', strtotime($media['display_date'] ?? $media['upload_date'])); ?></span>
                      </div>
                    </div>
                  </a>
                <?php else: ?>
                  <a href="asset/media/<?php echo htmlspecialchars($media['file_path']); ?>" target="_blank" class="block w-full h-full">
                    <img
                      class="w-full h-full object-cover rounded-lg lazy"
                      src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
                      data-src="<?php echo !empty($media['thumbnail']) ? 'asset/thumbnails/' . htmlspecialchars($media['thumbnail']) : 'asset/images/video_placeholder.jpg'; ?>"
                      alt="<?php echo htmlspecialchars($media['title']); ?>" />
                    <!-- Video play button -->
                    <div class="video-play-button">
                      <div class="w-10 h-10 bg-white bg-opacity-75 rounded-full flex items-center justify-center">
                        <div class="w-0 h-0 border-t-6 border-b-6 border-l-10 border-transparent border-l-blue-500 ml-1"></div>
                      </div>
                    </div>
                    <!-- Hover overlay with title -->
                    <div class="overlay-content">
                      <div class="text-white text-center p-2">
                        <p class="font-bold text-sm"><?php echo htmlspecialchars($media['title']); ?></p>
                        <span class="text-xs"><?php echo date('m/d/y', strtotime($media['display_date'] ?? $media['upload_date'])); ?></span>
                      </div>
                    </div>
                  </a>
                <?php endif; ?>
              </div>
            <?php endforeach; ?>
          <?php else: ?>
            <!-- Placeholder media items if none are available -->
            <div class="aspect-square bg-black rounded-lg relative overflow-hidden cursor-pointer item-hover">
              <a href="#" class="block w-full h-full">
                <img
                  class="w-full h-full object-cover rounded-lg lazy"
                  data-src="asset/images/field1.jpeg"
                  src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3C/svg%3E"
                  alt="Field Image" />
                <div class="overlay-content">
                  <div class="text-white text-center p-2">
                    <p class="font-bold text-sm">Field Image 1</p>
                    <span class="text-xs">Click to view</span>
                  </div>
                </div>
              </a>
            </div>
            <!-- Add more placeholder media items as needed -->
          <?php endif; ?>
        </div>

        <div class="w-full flex justify-center mt-3">
          <a href="field_interest_media.php" class="px-4 py-2 bg-blue-500 text-white font-bold rounded-lg cursor-pointer hover:bg-blue-600">View More</a>
        </div>
      </div>
    </section>

    <!-- Contact Section - Mobile Optimized -->
    <section id="contact" class="w-full flex flex-col items-center py-8 bg-gradient-to-t from-blue-500 to-white">
      <div class="w-full bg-white rounded-lg shadow-lg p-6 mb-8">
        <h2 class="text-xl font-bold text-center mb-4 text-gray-700">
          Send Message to <?php echo htmlspecialchars($data['honorific'] ?? 'Dr'); ?> <?php echo htmlspecialchars($data['name'] ?? 'Jayanta Debbarma'); ?>
        </h2>

        <form class="space-y-4" method="POST" action="src/send_mail.php">
          <div>
            <label class="block text-gray-600 font-medium mb-1 text-sm">Name</label>
            <input
              type="text"
              name="name"
              class="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-400 outline-none"
              placeholder="Enter your name"
              required />
          </div>

          <div>
            <label class="block text-gray-600 font-medium mb-1 text-sm">Email</label>
            <input
              type="email"
              name="email"
              class="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-400 outline-none"
              placeholder="Enter your email"
              required />
          </div>

          <div>
            <label class="block text-gray-600 font-medium mb-1 text-sm">Phone</label>
            <input
              type="tel"
              name="phone"
              class="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-400 outline-none"
              placeholder="Enter your phone number" />
          </div>

          <div>
            <label class="block text-gray-600 font-medium mb-1 text-sm">Message</label>
            <textarea
              name="message"
              class="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-400 outline-none resize-none h-24"
              placeholder="Enter your message"
              required></textarea>
          </div>

          <button
            type="submit"
            name="send"
            class="w-full bg-blue-500 text-white py-3 rounded-lg hover:bg-blue-600 transition duration-300 cursor-pointer">
            Send Message
          </button>
        </form>
      </div>
    </section>
  </main>

  <!-- Visitor Counter -->
  <p class="bg-blue-500 rounded-l-2xl" style="position: fixed; right: 0px; bottom: 10px; color: white; padding: 8px 20px; border: left 8px; z-index: 10000;">
    Visitors: <span id="visitorCount">Loading...</span>
  </p>

  <!-- Footer -->
  <footer class="w-full bg-blue-500 py-8 px-4 text-white">
    <div class="w-full flex flex-col items-center justify-center gap-4">
      <h2 class="text-3xl font-bold">Dr. Jayanta Debbarma</h2>
      <p class="text-center text-sm">© 2025 iLogitron Technologies (P) Ltd. All Right Reserved.</p>
    </div>
  </footer>

  <!-- Service Worker Registration -->
  <script src="src/register-sw.js" defer></script>

  <!-- WebP support and responsive images -->
  <script src="src/webp_support.js" defer></script>
  <script src="src/responsive-images.js" defer></script>

  <!-- Mobile-specific scripts -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const mobileMenuButton = document.getElementById('mobile-menu-button');
      const mobileNav = document.getElementById('mobile-nav');
      const mobileClose = document.getElementById('mobile-close');
      const mobileOverlay = document.querySelector('.mobile-menu-overlay');
      const mobileNavLinks = document.querySelectorAll('#mobile-nav a');

      // Open mobile menu
      mobileMenuButton.addEventListener('click', function() {
        mobileNav.classList.add('mobile-nav-active');
        mobileOverlay.classList.add('active');
        document.body.style.overflow = 'hidden'; // Prevent scrolling when menu is open
      });

      // Close mobile menu function
      function closeMobileMenu() {
        mobileNav.classList.remove('mobile-nav-active');
        mobileOverlay.classList.remove('active');
        document.body.style.overflow = ''; // Restore scrolling
      }

      // Close mobile menu when clicking the close button
      mobileClose.addEventListener('click', closeMobileMenu);

      // Close mobile menu when clicking the overlay
      mobileOverlay.addEventListener('click', closeMobileMenu);

      // Close mobile menu when clicking a link
      mobileNavLinks.forEach(link => {
        link.addEventListener('click', closeMobileMenu);
      });
    });

    // Enhanced progressive lazy loading for mobile
    document.addEventListener('DOMContentLoaded', function() {
      const lazyImages = Array.from(document.querySelectorAll('img.lazy'));

      // Add blur effect to all lazy images initially
      lazyImages.forEach(img => {
        img.style.filter = 'blur(5px)';
        img.style.transition = 'filter 0.5s ease-in-out';
      });

      // Function to load image with delay and unblur effect
      function loadImageWithEffect(img, delay) {
        setTimeout(() => {
          img.src = img.dataset.src;
          img.onload = function() {
            img.style.filter = 'blur(0)';
            img.classList.remove('lazy');
          };
        }, delay);
      }

      if ('IntersectionObserver' in window) {
        let lazyImageObserver = new IntersectionObserver(function(entries, observer) {
          entries.forEach(function(entry, index) {
            if (entry.isIntersecting) {
              let lazyImage = entry.target;
              // Add sequential delay based on image position
              const delay = index * 200; // 200ms delay between each image
              loadImageWithEffect(lazyImage, delay);
              lazyImageObserver.unobserve(lazyImage);
            }
          });
        }, {
          rootMargin: '100px' // Start loading a bit before they come into view
        });

        lazyImages.forEach(function(lazyImage) {
          lazyImageObserver.observe(lazyImage);
        });
      } else {
        // Fallback for browsers that don't support IntersectionObserver
        lazyImages.forEach(function(lazyImage, index) {
          const delay = index * 200;
          loadImageWithEffect(lazyImage, delay);
        });
      }
    });

    // Visitor counter - load after page content
    window.addEventListener('load', function() {
      // Fetch visitor count from the new visitor counter
      fetch('visitor.php?page=home')
        .then(response => {
          if (!response.ok) {
            throw new Error('Network response was not ok');
          }
          return response.text();
        })
        .then(data => {
          // Make sure we got a valid number
          const count = parseInt(data.trim());
          if (!isNaN(count) && count >= 0) {
            document.getElementById("visitorCount").textContent = count;
          } else {
            // Fallback to 0 if invalid response
            document.getElementById("visitorCount").textContent = "0";
          }
        })
        .catch(error => {
          console.error('Error fetching visitor count:', error);
          // Set default value on error
          document.getElementById("visitorCount").textContent = "0";
        });
    });
  </script>
</body>
</html>
