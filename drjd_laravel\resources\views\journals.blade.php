<x-layout.app page="journals">
    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-16">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto text-center">
                <h1 class="text-4xl md:text-5xl font-bold mb-6">{{ $structure->page4_title ?? 'Journals & Papers' }}</h1>
                <p class="text-xl">Research publications and academic contributions</p>
            </div>
        </div>
    </section>

    <!-- Publications Grid -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="max-w-6xl mx-auto">
                @if($journals->count() > 0)
                    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                        @foreach($journals as $journal)
                            <div class="bg-white rounded-lg shadow-lg hover:shadow-xl transition duration-300 overflow-hidden">
                                <div class="p-6">
                                    <div class="flex items-center mb-4">
                                        @if($journal->icon)
                                            <img src="{{ asset('asset/icons/' . $journal->icon) }}" alt="Icon" class="w-12 h-12 mr-4 rounded">
                                        @else
                                            <div class="w-12 h-12 bg-blue-100 rounded flex items-center justify-center mr-4">
                                                <i class="fas fa-file-alt text-blue-600 text-xl"></i>
                                            </div>
                                        @endif
                                        <div class="flex-1">
                                            <div class="text-sm text-gray-500">Publication</div>
                                            <div class="text-sm text-blue-600">{{ date('Y') }}</div>
                                        </div>
                                    </div>

                                    <h3 class="text-lg font-bold text-gray-800 mb-4 line-clamp-3">{{ $journal->name }}</h3>

                                    <div class="flex items-center justify-between">
                                        <a href="{{ $journal->link }}" target="_blank"
                                           class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition duration-300 flex items-center">
                                            <i class="fas fa-external-link-alt mr-2"></i>
                                            Read Paper
                                        </a>
                                        <div class="text-sm text-gray-500">
                                            <i class="fas fa-calendar mr-1"></i>
                                            {{ date('M Y') }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- Pagination would go here if needed -->
                    <div class="mt-12 text-center">
                        <p class="text-gray-600">Showing {{ $journals->count() }} publications</p>
                    </div>
                @else
                    <div class="text-center py-16">
                        <i class="fas fa-newspaper text-6xl text-gray-300 mb-6"></i>
                        <h3 class="text-2xl font-bold text-gray-600 mb-4">No Publications Yet</h3>
                        <p class="text-gray-500">Research publications will be displayed here once they are added.</p>
                    </div>
                @endif
            </div>
        </div>
    </section>

    <!-- Research Areas -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <h2 class="text-3xl font-bold text-center text-gray-800 mb-12">Research Areas</h2>

                <div class="grid md:grid-cols-3 gap-8">
                    <div class="text-center p-6 bg-white rounded-lg shadow">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-brain text-blue-600 text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-2">Data Science</h3>
                        <p class="text-gray-600">Advanced analytics and machine learning applications</p>
                    </div>

                    <div class="text-center p-6 bg-white rounded-lg shadow">
                        <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-chart-line text-indigo-600 text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-2">Statistical Analysis</h3>
                        <p class="text-gray-600">Statistical modeling and data interpretation</p>
                    </div>

                    <div class="text-center p-6 bg-white rounded-lg shadow">
                        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-microscope text-purple-600 text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-2">Research Methods</h3>
                        <p class="text-gray-600">Innovative research methodologies and approaches</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Publication Stats -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <h2 class="text-3xl font-bold text-center text-gray-800 mb-12">Publication Impact</h2>

                <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                    <div class="text-center p-6 bg-blue-50 rounded-lg">
                        <div class="text-3xl font-bold text-blue-600 mb-2">{{ $journals->count() }}</div>
                        <div class="text-gray-600">Total Publications</div>
                    </div>
                    <div class="text-center p-6 bg-indigo-50 rounded-lg">
                        <div class="text-3xl font-bold text-indigo-600 mb-2">500+</div>
                        <div class="text-gray-600">Citations</div>
                    </div>
                    <div class="text-center p-6 bg-purple-50 rounded-lg">
                        <div class="text-3xl font-bold text-purple-600 mb-2">15</div>
                        <div class="text-gray-600">H-Index</div>
                    </div>
                    <div class="text-center p-6 bg-pink-50 rounded-lg">
                        <div class="text-3xl font-bold text-pink-600 mb-2">25</div>
                        <div class="text-gray-600">Collaborations</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">Interested in Collaboration?</h2>
            <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
                I'm always open to research collaborations and academic partnerships.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('contact') }}" class="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition duration-300">
                    Contact Me
                </a>
                <a href="{{ route('thesis') }}" class="bg-gray-600 text-white px-8 py-3 rounded-lg hover:bg-gray-700 transition duration-300">
                    View Thesis Work
                </a>
            </div>
        </div>
    </section>
</x-layout.app>
