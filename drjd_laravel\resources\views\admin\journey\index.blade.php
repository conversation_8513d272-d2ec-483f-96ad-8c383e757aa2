<x-layout.admin title="Journey Management">
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Journey Management</h1>
                <p class="text-gray-600">Manage your education and experience timeline</p>
            </div>
            <a href="{{ route('admin.journey.create') }}" 
               class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition duration-300">
                <i class="fas fa-plus mr-2"></i>
                Add Journey Item
            </a>
        </div>
    </div>

    @if($journeys->count() > 0)
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Icon
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Title
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Subtitle
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Date
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($journeys as $journey)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($journey->icon)
                                    <div class="flex items-center">
                                        <img src="{{ asset('asset/images/' . $journey->icon) }}" 
                                             alt="Icon" class="h-8 w-8 rounded-full object-cover"
                                             onerror="this.src='{{ asset('asset/images/default-icon.png') }}'">
                                    </div>
                                @else
                                    <div class="h-8 w-8 bg-gray-200 rounded-full flex items-center justify-center">
                                        <i class="fas fa-graduation-cap text-gray-400"></i>
                                    </div>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900">{{ $journey->title }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-500">{{ $journey->sub_title }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ $journey->date }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex items-center justify-end space-x-2">
                                    <a href="{{ route('admin.journey.edit', $journey) }}" 
                                       class="text-indigo-600 hover:text-indigo-900">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form method="POST" action="{{ route('admin.journey.destroy', $journey) }}" 
                                          class="inline" onsubmit="return confirm('Are you sure you want to delete this journey item?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="text-red-600 hover:text-red-900">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    @else
        <div class="bg-white rounded-lg shadow p-6 text-center">
            <div class="text-gray-500 mb-4">
                <i class="fas fa-route text-4xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Journey Items Found</h3>
            <p class="text-gray-600 mb-4">Start building your education and experience timeline.</p>
            <a href="{{ route('admin.journey.create') }}" 
               class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition duration-300">
                <i class="fas fa-plus mr-2"></i>
                Add First Journey Item
            </a>
        </div>
    @endif
</x-layout.admin>
